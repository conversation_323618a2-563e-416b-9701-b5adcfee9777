import React, { createContext, useContext, useEffect, useRef, useState } from 'react';
import { io } from 'socket.io-client';
import toast from 'react-hot-toast';

const WebSocketContext = createContext();

export function useWebSocket() {
  const context = useContext(WebSocketContext);
  if (!context) {
    throw new Error('useWebSocket must be used within WebSocketProvider');
  }
  return context;
}

export function WebSocketProvider({ children, url }) {
  const [isConnected, setIsConnected] = useState(false);
  const [lastMessage, setLastMessage] = useState(null);
  const [connectionAttempts, setConnectionAttempts] = useState(0);
  const socketRef = useRef(null);
  const reconnectTimeoutRef = useRef(null);
  
  const MAX_RECONNECT_ATTEMPTS = 5;
  const RECONNECT_DELAY = 1000; // Start with 1 second
  
  // Initialize WebSocket connection
  useEffect(() => {
    if (!url) return;
    
    const connect = () => {
      try {
        socketRef.current = io(url, {
          transports: ['websocket', 'polling'],
          timeout: 5000,
          auth: {
            token: window.UFIOCommandCenter.nonce,
            userId: window.UFIOCommandCenter.user?.id
          },
          query: {
            plugin: 'ufio',
            version: window.UFIOCommandCenter.version
          }
        });
        
        const socket = socketRef.current;
        
        // Connection events
        socket.on('connect', () => {
          console.log('WebSocket connected');
          setIsConnected(true);
          setConnectionAttempts(0);
          
          toast.success('Real-time updates connected', {
            id: 'websocket-connected',
            duration: 2000
          });
          
          // Subscribe to relevant channels
          socket.emit('subscribe', {
            channels: [
              'processing_updates',
              'system_alerts',
              'stats_updates',
              'queue_updates'
            ]
          });
        });
        
        socket.on('disconnect', (reason) => {
          console.log('WebSocket disconnected:', reason);
          setIsConnected(false);
          
          if (reason === 'io server disconnect') {
            // Server initiated disconnect, don't reconnect automatically
            toast.error('Connection closed by server');
          } else {
            // Client disconnect, attempt reconnection
            scheduleReconnect();
          }
        });
        
        socket.on('connect_error', (error) => {
          console.error('WebSocket connection error:', error);
          setIsConnected(false);
          scheduleReconnect();
        });
        
        // Message handlers
        socket.on('processing_update', (data) => {
          setLastMessage({ type: 'processing_update', data, timestamp: Date.now() });
          
          if (data.type === 'batch_completed') {
            toast.success(`Batch completed: ${data.processed} posts processed`);
          } else if (data.type === 'post_processed') {
            // Silent update for individual posts
          }
        });
        
        socket.on('system_alert', (data) => {
          setLastMessage({ type: 'system_alert', data, timestamp: Date.now() });
          
          switch (data.level) {
            case 'error':
              toast.error(data.message, { duration: 6000 });
              break;
            case 'warning':
              toast(data.message, { 
                icon: '⚠️',
                duration: 4000
              });
              break;
            case 'info':
              toast(data.message, { 
                icon: 'ℹ️',
                duration: 3000
              });
              break;
            default:
              break;
          }
        });
        
        socket.on('stats_update', (data) => {
          setLastMessage({ type: 'stats_update', data, timestamp: Date.now() });
        });
        
        socket.on('queue_update', (data) => {
          setLastMessage({ type: 'queue_update', data, timestamp: Date.now() });
        });
        
        socket.on('microservice_status', (data) => {
          setLastMessage({ type: 'microservice_status', data, timestamp: Date.now() });
          
          if (data.status === 'healthy' && data.wasDown) {
            toast.success('Microservice reconnected');
          } else if (data.status === 'error') {
            toast.error('Microservice connection lost');
          }
        });
        
        // Performance monitoring
        socket.on('performance_metrics', (data) => {
          setLastMessage({ type: 'performance_metrics', data, timestamp: Date.now() });
        });
        
      } catch (error) {
        console.error('Failed to initialize WebSocket:', error);
        scheduleReconnect();
      }
    };
    
    const scheduleReconnect = () => {
      if (connectionAttempts >= MAX_RECONNECT_ATTEMPTS) {
        toast.error('Failed to connect to real-time updates after multiple attempts');
        return;
      }
      
      const delay = RECONNECT_DELAY * Math.pow(2, connectionAttempts); // Exponential backoff
      
      reconnectTimeoutRef.current = setTimeout(() => {
        setConnectionAttempts(prev => prev + 1);
        connect();
      }, delay);
    };
    
    // Initial connection
    connect();
    
    // Cleanup
    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
      
      if (socketRef.current) {
        socketRef.current.disconnect();
        socketRef.current = null;
      }
    };
  }, [url]);
  
  // Send message function
  const sendMessage = (message) => {
    if (socketRef.current && isConnected) {
      socketRef.current.emit('message', {
        ...message,
        timestamp: Date.now(),
        userId: window.UFIOCommandCenter.user?.id
      });
      return true;
    } else {
      toast.error('Not connected to real-time updates');
      return false;
    }
  };
  
  // Subscribe to specific channel
  const subscribe = (channel) => {
    if (socketRef.current && isConnected) {
      socketRef.current.emit('subscribe', { channels: [channel] });
    }
  };
  
  // Unsubscribe from channel
  const unsubscribe = (channel) => {
    if (socketRef.current && isConnected) {
      socketRef.current.emit('unsubscribe', { channels: [channel] });
    }
  };
  
  // Get connection status
  const getConnectionInfo = () => {
    return {
      isConnected,
      connectionAttempts,
      lastMessage,
      socketId: socketRef.current?.id,
      transport: socketRef.current?.io?.engine?.transport?.name
    };
  };
  
  const value = {
    isConnected,
    lastMessage,
    sendMessage,
    subscribe,
    unsubscribe,
    getConnectionInfo,
    connectionAttempts
  };
  
  return (
    <WebSocketContext.Provider value={value}>
      {children}
    </WebSocketContext.Provider>
  );
}
