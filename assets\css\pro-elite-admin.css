/**
 * 🚀 UFIO PRO ELITE - REVOLUTIONARY CSS FRAMEWORK
 * 
 * Ultra-modern, professional-grade design system with quantum performance
 * Built for enterprise-level WordPress plugins with advanced UI/UX patterns
 * 
 * @package UFIO_Pro_Elite
 * @version 10.0.0
 * <AUTHOR> Pro Elite Design Team
 */

/* ===== REVOLUTIONARY DESIGN TOKENS ===== */
:root {
  /* Elite Color Palette */
  --ufio-primary: #0066ff;
  --ufio-primary-light: #3385ff;
  --ufio-primary-dark: #0052cc;
  --ufio-success: #00d084;
  --ufio-success-light: #1ae6a0;
  --ufio-warning: #ff9500;
  --ufio-warning-light: #ffb84d;
  --ufio-error: #ff3366;
  --ufio-info: #17a2b8;
  
  /* Premium Gradients */
  --ufio-gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --ufio-gradient-success: linear-gradient(135deg, #00d084 0%, #1ae6a0 100%);
  --ufio-gradient-warning: linear-gradient(135deg, #ff9500 0%, #ffb84d 100%);
  --ufio-gradient-premium: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --ufio-gradient-elite: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
  --ufio-gradient-quantum: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  
  /* Professional Shadows */
  --ufio-shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
  --ufio-shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --ufio-shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --ufio-shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.15);
  --ufio-shadow-premium: 0 0 50px rgba(102, 126, 234, 0.3);
  
  /* Elite Typography */
  --ufio-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --ufio-font-mono: 'JetBrains Mono', 'Fira Code', monospace;
  
  /* Quantum Spacing */
  --ufio-space-xs: 0.25rem;
  --ufio-space-sm: 0.5rem;
  --ufio-space-md: 1rem;
  --ufio-space-lg: 1.5rem;
  --ufio-space-xl: 2rem;
  --ufio-space-2xl: 3rem;
  --ufio-space-3xl: 4rem;
  
  /* Professional Radius */
  --ufio-radius-sm: 0.375rem;
  --ufio-radius-md: 0.5rem;
  --ufio-radius-lg: 0.75rem;
  --ufio-radius-xl: 1rem;
  --ufio-radius-2xl: 1.5rem;
  
  /* Quantum Transitions */
  --ufio-transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  --ufio-transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --ufio-transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  --ufio-transition-bounce: 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* ===== REVOLUTIONARY BASE STYLES ===== */
.ufio-pro-elite-wrapper {
  font-family: var(--ufio-font-primary);
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  padding: var(--ufio-space-xl);
  color: #2d3748;
}

/* ===== PROFESSIONAL HEADER ===== */
.ufio-pro-elite-header {
  background: var(--ufio-gradient-primary);
  border-radius: var(--ufio-radius-2xl);
  padding: var(--ufio-space-3xl);
  margin-bottom: var(--ufio-space-2xl);
  box-shadow: var(--ufio-shadow-premium);
  position: relative;
  overflow: hidden;
  color: white;
}

.ufio-pro-elite-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 1;
}

.header-main h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 var(--ufio-space-md) 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-main .subtitle {
  font-size: 1.125rem;
  opacity: 0.9;
  margin: 0 0 var(--ufio-space-lg) 0;
  line-height: 1.6;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--ufio-space-sm);
  background: rgba(255, 255, 255, 0.2);
  padding: var(--ufio-space-sm) var(--ufio-space-md);
  border-radius: var(--ufio-radius-lg);
  font-size: 0.875rem;
  font-weight: 600;
  backdrop-filter: blur(10px);
}

.status-dot {
  width: 8px;
  height: 8px;
  background: #00d084;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.header-stats {
  display: flex;
  gap: var(--ufio-space-lg);
}

.quick-stat {
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  padding: var(--ufio-space-md);
  border-radius: var(--ufio-radius-lg);
  backdrop-filter: blur(10px);
  min-width: 100px;
}

.quick-stat .stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: var(--ufio-space-xs);
}

.quick-stat .stat-label {
  font-size: 0.75rem;
  opacity: 0.8;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* ===== REVOLUTIONARY STATISTICS GRID ===== */
.ufio-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--ufio-space-lg);
  margin-bottom: var(--ufio-space-2xl);
}

.stat-card {
  background: white;
  border-radius: var(--ufio-radius-xl);
  padding: var(--ufio-space-xl);
  box-shadow: var(--ufio-shadow-md);
  transition: var(--ufio-transition-normal);
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--ufio-gradient-primary);
}

.stat-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--ufio-shadow-xl);
}

.stat-card.success::before { background: var(--ufio-gradient-success); }
.stat-card.warning::before { background: var(--ufio-gradient-warning); }
.stat-card.premium::before { background: var(--ufio-gradient-premium); }
.stat-card.elite::before { background: var(--ufio-gradient-elite); }
.stat-card.quantum::before { background: var(--ufio-gradient-quantum); }

.stat-card {
  display: flex;
  align-items: center;
  gap: var(--ufio-space-md);
}

.stat-icon {
  font-size: 2.5rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--ufio-gradient-primary);
  border-radius: var(--ufio-radius-lg);
  flex-shrink: 0;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: var(--ufio-space-xs);
}

.stat-label {
  font-size: 0.875rem;
  color: #718096;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: var(--ufio-space-xs);
}

.stat-trend {
  font-size: 0.75rem;
  font-weight: 600;
  padding: var(--ufio-space-xs) var(--ufio-space-sm);
  border-radius: var(--ufio-radius-sm);
}

.stat-trend.positive {
  background: rgba(0, 208, 132, 0.1);
  color: #00d084;
}

.stat-trend.negative {
  background: rgba(255, 51, 102, 0.1);
  color: #ff3366;
}

.stat-trend.neutral {
  background: rgba(113, 128, 150, 0.1);
  color: #718096;
}

/* ===== PROFESSIONAL ACTION CARDS ===== */
.ufio-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--ufio-space-xl);
  margin-bottom: var(--ufio-space-2xl);
}

.action-card {
  background: white;
  border-radius: var(--ufio-radius-xl);
  padding: var(--ufio-space-xl);
  box-shadow: var(--ufio-shadow-md);
  transition: var(--ufio-transition-normal);
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.action-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--ufio-shadow-xl);
}

.action-card.premium-card {
  background: var(--ufio-gradient-premium);
  color: white;
}

.action-card.elite-card {
  background: var(--ufio-gradient-elite);
  color: #2d3748;
}

.action-card.professional-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.action-card.quantum-card {
  background: var(--ufio-gradient-quantum);
  color: #2d3748;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--ufio-space-md);
}

.card-icon {
  font-size: 2rem;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--ufio-radius-lg);
  backdrop-filter: blur(10px);
}

.card-badge {
  font-size: 0.75rem;
  font-weight: 700;
  padding: var(--ufio-space-xs) var(--ufio-space-sm);
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--ufio-radius-sm);
  backdrop-filter: blur(10px);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.action-card h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 var(--ufio-space-md) 0;
  line-height: 1.3;
}

.action-card p {
  margin: 0 0 var(--ufio-space-lg) 0;
  line-height: 1.6;
  opacity: 0.9;
}

.card-features {
  display: flex;
  flex-wrap: wrap;
  gap: var(--ufio-space-sm);
  margin-bottom: var(--ufio-space-lg);
}

.feature {
  font-size: 0.75rem;
  font-weight: 500;
  padding: var(--ufio-space-xs) var(--ufio-space-sm);
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--ufio-radius-sm);
  backdrop-filter: blur(10px);
}

/* ===== REVOLUTIONARY BUTTONS ===== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--ufio-space-sm);
  padding: var(--ufio-space-md) var(--ufio-space-xl);
  font-family: var(--ufio-font-primary);
  font-size: 0.875rem;
  font-weight: 600;
  text-decoration: none;
  border: none;
  border-radius: var(--ufio-radius-lg);
  cursor: pointer;
  transition: var(--ufio-transition-normal);
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  width: 100%;
}

.btn:focus {
  outline: 2px solid rgba(102, 126, 234, 0.5);
  outline-offset: 2px;
}

.btn-large {
  padding: var(--ufio-space-lg) var(--ufio-space-xl);
  font-size: 1rem;
}

.btn-primary {
  background: var(--ufio-gradient-primary);
  color: white;
  box-shadow: var(--ufio-shadow-md);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--ufio-shadow-lg);
}

.btn-success {
  background: var(--ufio-gradient-success);
  color: white;
  box-shadow: var(--ufio-shadow-md);
}

.btn-success:hover {
  transform: translateY(-2px);
  box-shadow: var(--ufio-shadow-lg);
}

.btn-warning {
  background: var(--ufio-gradient-warning);
  color: white;
  box-shadow: var(--ufio-shadow-md);
}

.btn-warning:hover {
  transform: translateY(-2px);
  box-shadow: var(--ufio-shadow-lg);
}

.btn-info {
  background: linear-gradient(135deg, #17a2b8 0%, #20c997 100%);
  color: white;
  box-shadow: var(--ufio-shadow-md);
}

.btn-info:hover {
  transform: translateY(-2px);
  box-shadow: var(--ufio-shadow-lg);
}

/* ===== PROFESSIONAL ANIMATIONS ===== */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
  from { opacity: 0; transform: translateX(-20px); }
  to { opacity: 1; transform: translateX(0); }
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .ufio-pro-elite-wrapper {
    padding: var(--ufio-space-md);
  }
  
  .ufio-pro-elite-header {
    padding: var(--ufio-space-xl);
  }
  
  .header-content {
    flex-direction: column;
    gap: var(--ufio-space-lg);
    text-align: center;
  }
  
  .header-main h1 {
    font-size: 2rem;
  }
  
  .ufio-stats-grid,
  .ufio-actions-grid {
    grid-template-columns: 1fr;
    gap: var(--ufio-space-md);
  }
  
  .stat-card,
  .action-card {
    padding: var(--ufio-space-lg);
  }
}

/* ===== PROFESSIONAL ALT TEXT MENU ===== */
.ufio-alt-text-menu {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--ufio-space-xl);
}

.menu-card {
  background: white;
  border-radius: var(--ufio-radius-2xl);
  box-shadow: var(--ufio-shadow-xl);
  max-width: 1000px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  animation: fadeIn 0.3s ease-out;
}

.menu-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--ufio-space-xl);
  border-bottom: 1px solid #e2e8f0;
  background: var(--ufio-gradient-primary);
  color: white;
  border-radius: var(--ufio-radius-2xl) var(--ufio-radius-2xl) 0 0;
}

.menu-header h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.close-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  font-size: 1.5rem;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--ufio-transition-fast);
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.alt-text-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--ufio-space-lg);
  padding: var(--ufio-space-xl);
}

.option-card {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: var(--ufio-radius-xl);
  padding: var(--ufio-space-xl);
  text-align: center;
  transition: var(--ufio-transition-normal);
  position: relative;
  overflow: hidden;
}

.option-card:hover {
  border-color: var(--ufio-primary);
  transform: translateY(-3px);
  box-shadow: var(--ufio-shadow-lg);
}

.option-card.premium {
  background: var(--ufio-gradient-success);
  color: white;
  border-color: transparent;
}

.option-card.professional {
  background: var(--ufio-gradient-primary);
  color: white;
  border-color: transparent;
}

.option-card.analytics {
  background: var(--ufio-gradient-warning);
  color: white;
  border-color: transparent;
}

.option-icon {
  font-size: 3rem;
  margin-bottom: var(--ufio-space-md);
  display: block;
}

.option-card h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 var(--ufio-space-md) 0;
}

.option-card p {
  margin: 0 0 var(--ufio-space-lg) 0;
  line-height: 1.6;
  opacity: 0.9;
}

/* ===== PROFESSIONAL RESULTS CONTAINER ===== */
.ufio-results-container {
  margin-top: var(--ufio-space-2xl);
  animation: fadeIn 0.5s ease-out;
}

.result-card {
  background: white;
  border-radius: var(--ufio-radius-xl);
  padding: var(--ufio-space-xl);
  box-shadow: var(--ufio-shadow-md);
  border-left: 4px solid var(--ufio-primary);
  position: relative;
  overflow: hidden;
}

.result-card.success {
  border-left-color: var(--ufio-success);
  background: linear-gradient(135deg, rgba(0, 208, 132, 0.05) 0%, rgba(255, 255, 255, 1) 100%);
}

.result-card.error {
  border-left-color: var(--ufio-error);
  background: linear-gradient(135deg, rgba(255, 51, 102, 0.05) 0%, rgba(255, 255, 255, 1) 100%);
}

.result-card.warning {
  border-left-color: var(--ufio-warning);
  background: linear-gradient(135deg, rgba(255, 149, 0, 0.05) 0%, rgba(255, 255, 255, 1) 100%);
}

.result-card.info {
  border-left-color: var(--ufio-info);
  background: linear-gradient(135deg, rgba(23, 162, 184, 0.05) 0%, rgba(255, 255, 255, 1) 100%);
}

.result-card.loading {
  border-left-color: var(--ufio-primary);
  background: linear-gradient(135deg, rgba(0, 102, 255, 0.05) 0%, rgba(255, 255, 255, 1) 100%);
  display: flex;
  align-items: center;
  gap: var(--ufio-space-md);
}

.result-card p {
  margin: 0;
  font-size: 1rem;
  line-height: 1.6;
  color: #2d3748;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid var(--ufio-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  flex-shrink: 0;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus styles for keyboard navigation */
.btn:focus,
.close-btn:focus {
  outline: 2px solid var(--ufio-primary);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .stat-card,
  .action-card,
  .menu-card,
  .result-card {
    border: 2px solid #000;
  }
}

/* Print styles */
@media print {
  .ufio-alt-text-menu,
  .btn,
  .loading-spinner {
    display: none !important;
  }

  .ufio-pro-elite-wrapper {
    background: white !important;
  }

  .stat-card,
  .action-card {
    box-shadow: none !important;
    border: 1px solid #000 !important;
  }
}
