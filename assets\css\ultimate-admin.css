/**
 * 🚀 UFIO PRO ULTIMATE - REVOLUTIONARY CSS FRAMEWORK
 * 
 * Ultra-modern, professional-grade design system with quantum performance
 * Built for enterprise-level WordPress plugins with advanced UI/UX patterns
 * 
 * @package UFIO_Pro_Ultimate
 * @version 100.0.0
 * <AUTHOR> Pro Ultimate Design Team
 */

/* ===== REVOLUTIONARY DESIGN TOKENS ===== */
:root {
  /* Ultimate Color Palette */
  --ufio-primary: #0066ff;
  --ufio-primary-light: #3385ff;
  --ufio-primary-dark: #0052cc;
  --ufio-success: #00d084;
  --ufio-success-light: #1ae6a0;
  --ufio-warning: #ff9500;
  --ufio-warning-light: #ffb84d;
  --ufio-error: #ff3366;
  --ufio-info: #17a2b8;
  
  /* Ultimate Gradients */
  --ufio-gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --ufio-gradient-success: linear-gradient(135deg, #00d084 0%, #1ae6a0 100%);
  --ufio-gradient-warning: linear-gradient(135deg, #ff9500 0%, #ffb84d 100%);
  --ufio-gradient-premium: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --ufio-gradient-elite: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
  --ufio-gradient-quantum: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  
  /* Professional Shadows */
  --ufio-shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
  --ufio-shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --ufio-shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --ufio-shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.15);
  --ufio-shadow-premium: 0 0 50px rgba(102, 126, 234, 0.3);
  
  /* Ultimate Typography */
  --ufio-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --ufio-font-mono: 'JetBrains Mono', 'Fira Code', monospace;
  
  /* Quantum Spacing */
  --ufio-space-xs: 0.25rem;
  --ufio-space-sm: 0.5rem;
  --ufio-space-md: 1rem;
  --ufio-space-lg: 1.5rem;
  --ufio-space-xl: 2rem;
  --ufio-space-2xl: 3rem;
  --ufio-space-3xl: 4rem;
  
  /* Professional Radius */
  --ufio-radius-sm: 0.375rem;
  --ufio-radius-md: 0.5rem;
  --ufio-radius-lg: 0.75rem;
  --ufio-radius-xl: 1rem;
  --ufio-radius-2xl: 1.5rem;
  
  /* Quantum Transitions */
  --ufio-transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  --ufio-transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --ufio-transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  --ufio-transition-bounce: 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* ===== REVOLUTIONARY BASE STYLES ===== */
.ufio-ultimate-wrapper {
  font-family: var(--ufio-font-primary);
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  padding: var(--ufio-space-xl);
  color: #2d3748;
}

/* ===== PROFESSIONAL HEADER ===== */
.ufio-ultimate-header {
  background: var(--ufio-gradient-primary);
  border-radius: var(--ufio-radius-2xl);
  padding: var(--ufio-space-3xl);
  margin-bottom: var(--ufio-space-2xl);
  box-shadow: var(--ufio-shadow-premium);
  position: relative;
  overflow: hidden;
  color: white;
  text-align: center;
}

.ufio-ultimate-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.ufio-ultimate-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 var(--ufio-space-md) 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
}

.ufio-ultimate-header .subtitle {
  font-size: 1.125rem;
  opacity: 0.9;
  margin: 0 0 var(--ufio-space-lg) 0;
  line-height: 1.6;
  position: relative;
  z-index: 1;
}

.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: var(--ufio-space-sm);
  background: rgba(255, 255, 255, 0.2);
  padding: var(--ufio-space-sm) var(--ufio-space-md);
  border-radius: var(--ufio-radius-lg);
  font-size: 0.875rem;
  font-weight: 600;
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 1;
}

.status-dot {
  width: 8px;
  height: 8px;
  background: #00d084;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

/* ===== REVOLUTIONARY STATISTICS GRID ===== */
.ufio-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--ufio-space-lg);
  margin-bottom: var(--ufio-space-2xl);
}

.stat-card {
  background: white;
  border-radius: var(--ufio-radius-xl);
  padding: var(--ufio-space-xl);
  box-shadow: var(--ufio-shadow-md);
  transition: var(--ufio-transition-normal);
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  gap: var(--ufio-space-md);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--ufio-gradient-primary);
}

.stat-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--ufio-shadow-xl);
}

.stat-card.success::before { background: var(--ufio-gradient-success); }
.stat-card.warning::before { background: var(--ufio-gradient-warning); }
.stat-card.info::before { background: var(--ufio-gradient-quantum); }

.stat-icon {
  font-size: 2.5rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--ufio-gradient-primary);
  border-radius: var(--ufio-radius-lg);
  flex-shrink: 0;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: var(--ufio-space-xs);
}

.stat-label {
  font-size: 0.875rem;
  color: #718096;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* ===== PROFESSIONAL ACTION CARDS ===== */
.ufio-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--ufio-space-xl);
  margin-bottom: var(--ufio-space-2xl);
}

.action-card {
  background: white;
  border-radius: var(--ufio-radius-xl);
  padding: var(--ufio-space-xl);
  box-shadow: var(--ufio-shadow-md);
  transition: var(--ufio-transition-normal);
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.action-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--ufio-shadow-xl);
}

.action-card.premium {
  background: var(--ufio-gradient-primary);
  color: white;
}

.action-card.elite {
  background: var(--ufio-gradient-success);
  color: white;
}

.action-card.professional {
  background: var(--ufio-gradient-warning);
  color: white;
}

.action-card.quantum {
  background: var(--ufio-gradient-quantum);
  color: #2d3748;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--ufio-space-md);
}

.card-icon {
  font-size: 2rem;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--ufio-radius-lg);
  backdrop-filter: blur(10px);
}

.card-badge {
  font-size: 0.75rem;
  font-weight: 700;
  padding: var(--ufio-space-xs) var(--ufio-space-sm);
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--ufio-radius-sm);
  backdrop-filter: blur(10px);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.action-card h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 var(--ufio-space-md) 0;
  line-height: 1.3;
}

.action-card p {
  margin: 0 0 var(--ufio-space-lg) 0;
  line-height: 1.6;
  opacity: 0.9;
}

/* ===== REVOLUTIONARY BUTTONS ===== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--ufio-space-sm);
  padding: var(--ufio-space-md) var(--ufio-space-xl);
  font-family: var(--ufio-font-primary);
  font-size: 0.875rem;
  font-weight: 600;
  text-decoration: none;
  border: none;
  border-radius: var(--ufio-radius-lg);
  cursor: pointer;
  transition: var(--ufio-transition-normal);
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  width: 100%;
}

.btn:focus {
  outline: 2px solid rgba(102, 126, 234, 0.5);
  outline-offset: 2px;
}

.btn-large {
  padding: var(--ufio-space-lg) var(--ufio-space-xl);
  font-size: 1rem;
}

.btn-primary {
  background: var(--ufio-gradient-primary);
  color: white;
  box-shadow: var(--ufio-shadow-md);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--ufio-shadow-lg);
}

.btn-success {
  background: var(--ufio-gradient-success);
  color: white;
  box-shadow: var(--ufio-shadow-md);
}

.btn-success:hover {
  transform: translateY(-2px);
  box-shadow: var(--ufio-shadow-lg);
}

.btn-warning {
  background: var(--ufio-gradient-warning);
  color: white;
  box-shadow: var(--ufio-shadow-md);
}

.btn-warning:hover {
  transform: translateY(-2px);
  box-shadow: var(--ufio-shadow-lg);
}

.btn-info {
  background: linear-gradient(135deg, #17a2b8 0%, #20c997 100%);
  color: white;
  box-shadow: var(--ufio-shadow-md);
}

.btn-info:hover {
  transform: translateY(-2px);
  box-shadow: var(--ufio-shadow-lg);
}

/* ===== PROFESSIONAL ANIMATIONS ===== */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* ===== ENHANCED RESULTS DISPLAY ===== */
.ufio-results-container {
  margin-top: var(--ufio-space-2xl);
  animation: fadeIn 0.5s ease-out;
}

.result-card {
  background: white;
  border-radius: var(--ufio-radius-xl);
  padding: var(--ufio-space-xl);
  box-shadow: var(--ufio-shadow-md);
  border-left: 4px solid var(--ufio-primary);
  position: relative;
  overflow: hidden;
}

.result-card.success {
  border-left-color: var(--ufio-success);
  background: linear-gradient(135deg, rgba(0, 208, 132, 0.05) 0%, rgba(255, 255, 255, 1) 100%);
}

.result-card.error {
  border-left-color: var(--ufio-error);
  background: linear-gradient(135deg, rgba(255, 51, 102, 0.05) 0%, rgba(255, 255, 255, 1) 100%);
}

.result-card.warning {
  border-left-color: var(--ufio-warning);
  background: linear-gradient(135deg, rgba(255, 149, 0, 0.05) 0%, rgba(255, 255, 255, 1) 100%);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--ufio-space-lg);
  padding-bottom: var(--ufio-space-md);
  border-bottom: 1px solid #e2e8f0;
}

.result-header h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  color: #2d3748;
}

.result-stats {
  display: flex;
  gap: var(--ufio-space-sm);
  flex-wrap: wrap;
}

.stat-badge {
  font-size: 0.75rem;
  font-weight: 600;
  padding: var(--ufio-space-xs) var(--ufio-space-sm);
  border-radius: var(--ufio-radius-sm);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.stat-badge.primary {
  background: rgba(0, 102, 255, 0.1);
  color: var(--ufio-primary);
}

.stat-badge.success {
  background: rgba(0, 208, 132, 0.1);
  color: var(--ufio-success);
}

.stat-badge.warning {
  background: rgba(255, 149, 0, 0.1);
  color: var(--ufio-warning);
}

.result-content {
  line-height: 1.6;
}

.result-message {
  font-size: 1.125rem;
  font-weight: 500;
  margin-bottom: var(--ufio-space-lg);
  color: #2d3748;
}

.detailed-results {
  background: #f8fafc;
  border-radius: var(--ufio-radius-lg);
  padding: var(--ufio-space-lg);
  margin-bottom: var(--ufio-space-lg);
}

.detailed-results h4 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 var(--ufio-space-md) 0;
  color: #2d3748;
}

.results-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.results-list li {
  padding: var(--ufio-space-sm) 0;
  border-bottom: 1px solid #e2e8f0;
  color: #4a5568;
}

.results-list li:last-child {
  border-bottom: none;
}

.prompts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--ufio-space-md);
  margin-top: var(--ufio-space-md);
}

.prompt-card {
  background: white;
  border-radius: var(--ufio-radius-lg);
  padding: var(--ufio-space-md);
  box-shadow: var(--ufio-shadow-sm);
  border: 1px solid #e2e8f0;
}

.prompt-card h5 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 var(--ufio-space-sm) 0;
  color: #2d3748;
}

.prompt-details {
  font-size: 0.875rem;
  color: #718096;
  margin-bottom: var(--ufio-space-sm);
}

.prompt-details p {
  margin: 0 0 var(--ufio-space-xs) 0;
}

.ai-prompts {
  margin-top: var(--ufio-space-sm);
}

.prompt-item {
  background: #f7fafc;
  border-radius: var(--ufio-radius-sm);
  padding: var(--ufio-space-sm);
  margin-bottom: var(--ufio-space-xs);
}

.prompt-type {
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  color: var(--ufio-primary);
  letter-spacing: 0.05em;
}

.prompt-text {
  font-size: 0.875rem;
  color: #4a5568;
  margin: var(--ufio-space-xs) 0 0 0;
  line-height: 1.5;
}

.next-steps {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(255, 255, 255, 1) 100%);
  border-radius: var(--ufio-radius-lg);
  padding: var(--ufio-space-lg);
  margin-top: var(--ufio-space-lg);
}

.next-steps h4 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 var(--ufio-space-md) 0;
  color: #2d3748;
}

.next-steps ul {
  margin: var(--ufio-space-sm) 0 0 var(--ufio-space-lg);
  color: #4a5568;
}

.next-steps li {
  margin-bottom: var(--ufio-space-xs);
  line-height: 1.5;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .ufio-ultimate-wrapper {
    padding: var(--ufio-space-md);
  }
  
  .ufio-ultimate-header {
    padding: var(--ufio-space-xl);
  }
  
  .ufio-ultimate-header h1 {
    font-size: 2rem;
  }
  
  .ufio-stats-grid,
  .ufio-actions-grid {
    grid-template-columns: 1fr;
    gap: var(--ufio-space-md);
  }
  
  .stat-card,
  .action-card {
    padding: var(--ufio-space-lg);
  }
  
  .prompts-grid {
    grid-template-columns: 1fr;
  }
  
  .result-stats {
    flex-direction: column;
    align-items: flex-start;
  }
}
