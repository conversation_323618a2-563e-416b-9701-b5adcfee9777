/**
 * UFIO Pro Dashboard - Enhanced Styles
 * Beautiful, modern dashboard with animations and responsive design
 */

:root {
    --ufio-primary: #667eea;
    --ufio-primary-dark: #5a67d8;
    --ufio-secondary: #764ba2;
    --ufio-success: #48bb78;
    --ufio-warning: #ed8936;
    --ufio-error: #f56565;
    --ufio-info: #4299e1;
    --ufio-gray-50: #f7fafc;
    --ufio-gray-100: #edf2f7;
    --ufio-gray-200: #e2e8f0;
    --ufio-gray-300: #cbd5e0;
    --ufio-gray-400: #a0aec0;
    --ufio-gray-500: #718096;
    --ufio-gray-600: #4a5568;
    --ufio-gray-700: #2d3748;
    --ufio-gray-800: #1a202c;
    --ufio-gray-900: #171923;
    --ufio-white: #ffffff;
    --ufio-shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --ufio-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --ufio-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --ufio-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --ufio-border-radius: 12px;
    --ufio-border-radius-lg: 16px;
    --ufio-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --ufio-gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --ufio-gradient-success: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    --ufio-gradient-warning: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
    --ufio-gradient-info: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
}

/* Dashboard Wrapper */
.ufio-dashboard-wrap {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    background: var(--ufio-gray-50);
    min-height: 100vh;
}

/* Dashboard Header */
.ufio-dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1.5rem 2rem;
    background: var(--ufio-white);
    border-radius: var(--ufio-border-radius-lg);
    box-shadow: var(--ufio-shadow);
    background: var(--ufio-gradient-primary);
    color: var(--ufio-white);
}

.ufio-dashboard-header h1 {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin: 0;
    font-size: 2rem;
    font-weight: 700;
    color: var(--ufio-white);
}

.ufio-logo {
    font-size: 2.5rem;
    animation: ufio-pulse 2s infinite;
}

.ufio-version {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
}

.ufio-header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.ufio-status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    padding: 0.5rem 1rem;
    border-radius: 25px;
    backdrop-filter: blur(10px);
}

.ufio-status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    animation: ufio-blink 2s infinite;
}

.ufio-status-dot.success { background: #48bb78; }
.ufio-status-dot.warning { background: #ed8936; }
.ufio-status-dot.error { background: #f56565; }

/* Metrics Grid */
.ufio-metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.ufio-metric-card {
    background: var(--ufio-white);
    border-radius: var(--ufio-border-radius);
    padding: 1.5rem;
    box-shadow: var(--ufio-shadow);
    transition: var(--ufio-transition);
    position: relative;
    overflow: hidden;
}

.ufio-metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--ufio-gradient-primary);
}

.ufio-metric-card.primary::before { background: var(--ufio-gradient-primary); }
.ufio-metric-card.success::before { background: var(--ufio-gradient-success); }
.ufio-metric-card.warning::before { background: var(--ufio-gradient-warning); }
.ufio-metric-card.info::before { background: var(--ufio-gradient-info); }

.ufio-metric-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--ufio-shadow-lg);
}

.ufio-metric-card {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.ufio-metric-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

.ufio-metric-content {
    flex: 1;
}

.ufio-metric-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--ufio-gray-800);
    line-height: 1;
    margin-bottom: 0.25rem;
}

.ufio-metric-label {
    font-size: 0.875rem;
    color: var(--ufio-gray-600);
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.ufio-metric-change {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    display: inline-block;
}

.ufio-metric-change.positive {
    background: rgba(72, 187, 120, 0.1);
    color: var(--ufio-success);
}

.ufio-metric-change.negative {
    background: rgba(245, 101, 101, 0.1);
    color: var(--ufio-error);
}

.ufio-metric-change.neutral {
    background: var(--ufio-gray-100);
    color: var(--ufio-gray-600);
}

/* Dashboard Grid */
.ufio-dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

/* Dashboard Cards */
.ufio-dashboard-card {
    background: var(--ufio-white);
    border-radius: var(--ufio-border-radius);
    box-shadow: var(--ufio-shadow);
    transition: var(--ufio-transition);
    overflow: hidden;
}

.ufio-dashboard-card.full-width {
    grid-column: 1 / -1;
}

.ufio-dashboard-card:hover {
    box-shadow: var(--ufio-shadow-lg);
}

.ufio-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 1.5rem 0;
    margin-bottom: 1rem;
}

.ufio-card-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--ufio-gray-800);
}

.ufio-card-actions {
    display: flex;
    gap: 0.5rem;
}

.ufio-card-content {
    padding: 0 1.5rem 1.5rem;
}

.ufio-card-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--ufio-gray-200);
    background: var(--ufio-gray-50);
}

/* Priority Posts List */
.ufio-priority-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.ufio-priority-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--ufio-gray-50);
    border-radius: var(--ufio-border-radius);
    transition: var(--ufio-transition);
}

.ufio-priority-item:hover {
    background: var(--ufio-gray-100);
    transform: translateX(4px);
}

.ufio-priority-score {
    background: var(--ufio-gradient-primary);
    color: var(--ufio-white);
    padding: 0.5rem;
    border-radius: 8px;
    font-weight: 700;
    font-size: 0.875rem;
    min-width: 50px;
    text-align: center;
}

.ufio-priority-content {
    flex: 1;
}

.ufio-priority-title {
    font-weight: 600;
    color: var(--ufio-gray-800);
    margin-bottom: 0.25rem;
}

.ufio-priority-meta {
    font-size: 0.75rem;
    color: var(--ufio-gray-600);
}

/* Performance Metrics */
.ufio-performance-metrics {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.ufio-performance-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.ufio-performance-label {
    font-size: 0.875rem;
    color: var(--ufio-gray-600);
    font-weight: 500;
}

.ufio-performance-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--ufio-gray-800);
}

.ufio-performance-bar {
    height: 8px;
    background: var(--ufio-gray-200);
    border-radius: 4px;
    overflow: hidden;
}

.ufio-performance-fill {
    height: 100%;
    background: var(--ufio-gradient-success);
    border-radius: 4px;
    transition: width 0.8s ease;
}

.ufio-performance-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.ufio-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.ufio-indicator.excellent { background: var(--ufio-success); }
.ufio-indicator.good { background: var(--ufio-info); }
.ufio-indicator.fair { background: var(--ufio-warning); }
.ufio-indicator.poor { background: var(--ufio-error); }

/* Action Grid */
.ufio-action-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.ufio-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
    padding: 1.5rem 1rem;
    background: var(--ufio-gray-50);
    border: 2px solid var(--ufio-gray-200);
    border-radius: var(--ufio-border-radius);
    transition: var(--ufio-transition);
    cursor: pointer;
    text-align: center;
}

.ufio-action-btn:hover {
    background: var(--ufio-white);
    border-color: var(--ufio-primary);
    transform: translateY(-2px);
    box-shadow: var(--ufio-shadow);
}

.ufio-action-icon {
    font-size: 2rem;
}

.ufio-action-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--ufio-gray-700);
}

/* Activity Feed */
.ufio-activity-feed {
    max-height: 400px;
    overflow-y: auto;
    padding-right: 0.5rem;
}

.ufio-activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-bottom: 1px solid var(--ufio-gray-200);
    transition: var(--ufio-transition);
}

.ufio-activity-item:hover {
    background: var(--ufio-gray-50);
}

.ufio-activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: var(--ufio-white);
}

.ufio-activity-icon.success { background: var(--ufio-success); }
.ufio-activity-icon.warning { background: var(--ufio-warning); }
.ufio-activity-icon.error { background: var(--ufio-error); }
.ufio-activity-icon.info { background: var(--ufio-info); }

.ufio-activity-content {
    flex: 1;
}

.ufio-activity-title {
    font-weight: 600;
    color: var(--ufio-gray-800);
    margin-bottom: 0.25rem;
}

.ufio-activity-meta {
    font-size: 0.75rem;
    color: var(--ufio-gray-600);
}

.ufio-activity-time {
    font-size: 0.75rem;
    color: var(--ufio-gray-500);
    white-space: nowrap;
}

/* Buttons */
.ufio-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--ufio-border-radius);
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: var(--ufio-transition);
    text-decoration: none;
}

.ufio-btn.primary {
    background: var(--ufio-gradient-primary);
    color: var(--ufio-white);
}

.ufio-btn.primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--ufio-shadow-lg);
}

.ufio-btn.secondary {
    background: var(--ufio-white);
    color: var(--ufio-gray-700);
    border: 2px solid var(--ufio-gray-300);
}

.ufio-btn.secondary:hover {
    border-color: var(--ufio-primary);
    color: var(--ufio-primary);
}

.ufio-btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
}

.ufio-btn-icon {
    padding: 0.5rem;
    background: transparent;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: var(--ufio-transition);
}

.ufio-btn-icon:hover {
    background: var(--ufio-gray-100);
}

/* Badges */
.ufio-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    background: var(--ufio-primary);
    color: var(--ufio-white);
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

/* Animations */
@keyframes ufio-pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes ufio-blink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes ufio-slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.ufio-slide-in {
    animation: ufio-slideIn 0.5s ease-out;
}

/* Responsive Design */
@media (max-width: 768px) {
    .ufio-dashboard-wrap {
        padding: 1rem;
    }
    
    .ufio-dashboard-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .ufio-metrics-grid {
        grid-template-columns: 1fr;
    }
    
    .ufio-dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .ufio-action-grid {
        grid-template-columns: 1fr;
    }
}

/* Loading States */
.ufio-loading {
    position: relative;
    overflow: hidden;
}

.ufio-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: ufio-shimmer 1.5s infinite;
}

@keyframes ufio-shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Tooltips */
[data-tooltip] {
    position: relative;
}

[data-tooltip]:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--ufio-gray-800);
    color: var(--ufio-white);
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    font-size: 0.75rem;
    white-space: nowrap;
    z-index: 1000;
    margin-bottom: 0.5rem;
}

/* Scrollbar Styling */
.ufio-activity-feed::-webkit-scrollbar {
    width: 6px;
}

.ufio-activity-feed::-webkit-scrollbar-track {
    background: var(--ufio-gray-100);
    border-radius: 3px;
}

.ufio-activity-feed::-webkit-scrollbar-thumb {
    background: var(--ufio-gray-400);
    border-radius: 3px;
}

.ufio-activity-feed::-webkit-scrollbar-thumb:hover {
    background: var(--ufio-gray-500);
}

/* Enhanced Notifications */
#ufio-notifications {
    position: fixed;
    top: 32px;
    right: 20px;
    z-index: 999999;
    pointer-events: none;
    max-width: 400px;
}

.ufio-notification {
    background: var(--ufio-white);
    border-radius: var(--ufio-border-radius);
    box-shadow: var(--ufio-shadow-xl);
    margin-bottom: 1rem;
    transform: translateX(100%);
    transition: var(--ufio-transition);
    pointer-events: auto;
    overflow: hidden;
    border-left: 4px solid var(--ufio-primary);
}

.ufio-notification.show {
    transform: translateX(0);
}

.ufio-notification-success {
    border-left-color: var(--ufio-success);
}

.ufio-notification-error {
    border-left-color: var(--ufio-error);
}

.ufio-notification-warning {
    border-left-color: var(--ufio-warning);
}

.ufio-notification-info {
    border-left-color: var(--ufio-info);
}

.ufio-notification-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 1.5rem;
}

.ufio-notification-icon {
    font-size: 1.25rem;
    flex-shrink: 0;
}

.ufio-notification-message {
    flex: 1;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--ufio-gray-800);
    line-height: 1.4;
}

.ufio-notification-close {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    background: none;
    border: none;
    font-size: 1.25rem;
    color: var(--ufio-gray-400);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: var(--ufio-transition);
}

.ufio-notification-close:hover {
    background: var(--ufio-gray-100);
    color: var(--ufio-gray-600);
}

/* Loading States Enhancement */
.ufio-updating {
    position: relative;
    color: var(--ufio-primary) !important;
}

.ufio-updating::after {
    content: '';
    position: absolute;
    top: 50%;
    right: -20px;
    width: 12px;
    height: 12px;
    border: 2px solid var(--ufio-primary);
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: ufio-spin 1s linear infinite;
    transform: translateY(-50%);
}

@keyframes ufio-spin {
    0% { transform: translateY(-50%) rotate(0deg); }
    100% { transform: translateY(-50%) rotate(360deg); }
}

/* Enhanced Button States */
.ufio-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.ufio-btn.success {
    background: var(--ufio-gradient-success);
    color: var(--ufio-white);
}

.ufio-btn.error {
    background: var(--ufio-gradient-warning);
    color: var(--ufio-white);
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --ufio-white: #1a202c;
        --ufio-gray-50: #2d3748;
        --ufio-gray-100: #4a5568;
        --ufio-gray-200: #718096;
        --ufio-gray-800: #f7fafc;
        --ufio-gray-900: #ffffff;
    }

    .ufio-notification {
        background: var(--ufio-gray-800);
        color: var(--ufio-white);
    }

    .ufio-notification-message {
        color: var(--ufio-white);
    }
}
