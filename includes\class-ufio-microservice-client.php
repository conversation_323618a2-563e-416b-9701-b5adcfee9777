<?php
/**
 * UFIO Microservice Client
 * Handles communication with external image processing microservice
 */

if (!defined('ABSPATH')) {
    exit;
}

class UFIO_Microservice_Client {
    
    private static $instance = null;
    private $service_url = '';
    private $api_key = '';
    private $timeout = 30;
    private $retry_attempts = 3;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        $options = get_option('ufio_options', []);
        $this->service_url = $options['microservice_url'] ?? '';
        $this->api_key = $options['microservice_api_key'] ?? '';
        
        // Schedule microservice job processing
        add_action('ufio_process_microservice_jobs', [$this, 'process_pending_jobs']);
        
        if (!wp_next_scheduled('ufio_process_microservice_jobs')) {
            wp_schedule_event(time(), 'ufio_2min', 'ufio_process_microservice_jobs');
        }
    }
    
    /**
     * Queue image search job
     */
    public function queue_image_search($post_id, $analysis) {
        if (empty($this->service_url)) {
            return false; // Microservice not configured
        }
        
        global $wpdb;
        $microservice_table = $wpdb->prefix . 'ufio_microservice_queue';
        
        $payload = [
            'post_id' => $post_id,
            'analysis' => $analysis,
            'site_url' => home_url(),
            'wp_api_url' => rest_url('wp/v2/'),
            'auth_token' => $this->generate_auth_token($post_id)
        ];
        
        $job_id = $wpdb->insert($microservice_table, [
            'job_type' => 'image_search',
            'post_id' => $post_id,
            'payload' => json_encode($payload),
            'priority' => $this->calculate_job_priority($analysis),
            'status' => 'pending'
        ]);
        
        return $job_id ? $wpdb->insert_id : false;
    }
    
    /**
     * Queue embedding generation job
     */
    public function queue_embedding_generation($attachment_id) {
        if (empty($this->service_url)) {
            return false;
        }
        
        global $wpdb;
        $microservice_table = $wpdb->prefix . 'ufio_microservice_queue';
        
        $image_url = wp_get_attachment_url($attachment_id);
        if (!$image_url) {
            return false;
        }
        
        $payload = [
            'attachment_id' => $attachment_id,
            'image_url' => $image_url,
            'site_url' => home_url(),
            'auth_token' => $this->generate_auth_token($attachment_id)
        ];
        
        $job_id = $wpdb->insert($microservice_table, [
            'job_type' => 'embedding_generation',
            'attachment_id' => $attachment_id,
            'payload' => json_encode($payload),
            'priority' => 3, // Medium priority
            'status' => 'pending'
        ]);
        
        return $job_id ? $wpdb->insert_id : false;
    }
    
    /**
     * Queue SEO analysis job
     */
    public function queue_seo_analysis($post_id, $image_ids) {
        if (empty($this->service_url)) {
            return false;
        }
        
        global $wpdb;
        $microservice_table = $wpdb->prefix . 'ufio_microservice_queue';
        
        $post = get_post($post_id);
        if (!$post) {
            return false;
        }
        
        $payload = [
            'post_id' => $post_id,
            'post_title' => $post->post_title,
            'post_content' => wp_strip_all_tags($post->post_content),
            'image_ids' => $image_ids,
            'site_url' => home_url(),
            'auth_token' => $this->generate_auth_token($post_id)
        ];
        
        $job_id = $wpdb->insert($microservice_table, [
            'job_type' => 'seo_analysis',
            'post_id' => $post_id,
            'payload' => json_encode($payload),
            'priority' => 4, // Lower priority
            'status' => 'pending'
        ]);
        
        return $job_id ? $wpdb->insert_id : false;
    }
    
    /**
     * Process pending microservice jobs
     */
    public function process_pending_jobs($max_jobs = 20) {
        global $wpdb;
        
        $microservice_table = $wpdb->prefix . 'ufio_microservice_queue';
        
        // Get highest priority pending jobs
        $jobs = $wpdb->get_results($wpdb->prepare("
            SELECT * FROM $microservice_table 
            WHERE status = 'pending' 
            AND attempts < max_attempts
            ORDER BY priority DESC, created_at ASC
            LIMIT %d
        ", $max_jobs), ARRAY_A);
        
        if (empty($jobs)) {
            return 0;
        }
        
        $processed = 0;
        
        foreach ($jobs as $job) {
            if ($this->process_microservice_job($job)) {
                $processed++;
            }
        }
        
        return $processed;
    }
    
    /**
     * Process individual microservice job
     */
    private function process_microservice_job($job) {
        global $wpdb;
        
        $microservice_table = $wpdb->prefix . 'ufio_microservice_queue';
        $job_id = $job['id'];
        
        // Mark as processing
        $wpdb->update($microservice_table, [
            'status' => 'processing',
            'attempts' => $job['attempts'] + 1,
            'updated_at' => current_time('mysql')
        ], ['id' => $job_id]);
        
        try {
            $start_time = microtime(true);
            
            $response = $this->call_microservice($job['job_type'], json_decode($job['payload'], true));
            
            $processing_time = (microtime(true) - $start_time) * 1000;
            
            if ($response && $response['success']) {
                // Mark as completed
                $wpdb->update($microservice_table, [
                    'status' => 'completed',
                    'response' => json_encode($response),
                    'processing_time_ms' => $processing_time,
                    'completed_at' => current_time('mysql')
                ], ['id' => $job_id]);
                
                // Process the response
                $this->handle_microservice_response($job, $response);
                
                return true;
            } else {
                throw new Exception($response['error'] ?? 'Unknown microservice error');
            }
            
        } catch (Exception $e) {
            $this->handle_microservice_failure($job_id, $job, $e->getMessage());
            return false;
        }
    }
    
    /**
     * Call microservice endpoint
     */
    private function call_microservice($job_type, $payload) {
        $endpoint_map = [
            'image_search' => '/api/v1/image-search',
            'embedding_generation' => '/api/v1/generate-embedding',
            'seo_analysis' => '/api/v1/seo-analysis'
        ];
        
        $endpoint = $endpoint_map[$job_type] ?? null;
        if (!$endpoint) {
            throw new Exception('Unknown job type: ' . $job_type);
        }
        
        $url = rtrim($this->service_url, '/') . $endpoint;
        
        $response = wp_remote_post($url, [
            'headers' => [
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . $this->api_key,
                'X-WordPress-Site' => home_url()
            ],
            'body' => json_encode($payload),
            'timeout' => $this->timeout
        ]);
        
        if (is_wp_error($response)) {
            throw new Exception('HTTP request failed: ' . $response->get_error_message());
        }
        
        $status_code = wp_remote_retrieve_response_code($response);
        if ($status_code !== 200) {
            throw new Exception('Microservice returned error code: ' . $status_code);
        }
        
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);
        
        if (!$data) {
            throw new Exception('Invalid JSON response from microservice');
        }
        
        return $data;
    }
    
    /**
     * Handle successful microservice response
     */
    private function handle_microservice_response($job, $response) {
        switch ($job['job_type']) {
            case 'image_search':
                $this->handle_image_search_response($job, $response);
                break;
                
            case 'embedding_generation':
                $this->handle_embedding_response($job, $response);
                break;
                
            case 'seo_analysis':
                $this->handle_seo_analysis_response($job, $response);
                break;
        }
    }
    
    /**
     * Handle image search response
     */
    private function handle_image_search_response($job, $response) {
        $payload = json_decode($job['payload'], true);
        $post_id = $payload['post_id'];
        
        if (isset($response['data']['best_image_id']) && $response['data']['best_image_id']) {
            $image_id = $response['data']['best_image_id'];
            
            // Set featured image
            set_post_thumbnail($post_id, $image_id);
            
            // Store additional metadata
            if (isset($response['data']['relevance_score'])) {
                update_post_meta($post_id, '_ufio_relevance_score', $response['data']['relevance_score']);
            }
            
            if (isset($response['data']['seo_optimizations'])) {
                $this->apply_seo_optimizations($image_id, $response['data']['seo_optimizations']);
            }
            
            // Log success
            $this->log_analytics('microservice_success', $post_id, $job['id'], [
                'job_type' => 'image_search',
                'image_id' => $image_id,
                'relevance_score' => $response['data']['relevance_score'] ?? null
            ]);
        }
    }
    
    /**
     * Handle embedding generation response
     */
    private function handle_embedding_response($job, $response) {
        $payload = json_decode($job['payload'], true);
        $attachment_id = $payload['attachment_id'];
        
        if (isset($response['data']['embedding'])) {
            global $wpdb;
            $image_cache_table = $wpdb->prefix . 'ufio_image_cache';
            
            $wpdb->replace($image_cache_table, [
                'attachment_id' => $attachment_id,
                'clip_embedding' => json_encode($response['data']['embedding']),
                'keywords_json' => json_encode($response['data']['keywords'] ?? []),
                'seo_score' => $response['data']['seo_score'] ?? null,
                'last_analyzed' => current_time('mysql')
            ]);
        }
    }
    
    /**
     * Handle SEO analysis response
     */
    private function handle_seo_analysis_response($job, $response) {
        $payload = json_decode($job['payload'], true);
        $post_id = $payload['post_id'];
        
        if (isset($response['data']['optimizations'])) {
            foreach ($response['data']['optimizations'] as $optimization) {
                if (isset($optimization['image_id'])) {
                    $this->apply_seo_optimizations($optimization['image_id'], $optimization);
                }
            }
        }
    }
    
    /**
     * Apply SEO optimizations to image
     */
    private function apply_seo_optimizations($image_id, $optimizations) {
        if (isset($optimizations['alt_text'])) {
            update_post_meta($image_id, '_wp_attachment_image_alt', $optimizations['alt_text']);
        }
        
        if (isset($optimizations['title'])) {
            wp_update_post([
                'ID' => $image_id,
                'post_title' => $optimizations['title']
            ]);
        }
        
        if (isset($optimizations['caption'])) {
            wp_update_post([
                'ID' => $image_id,
                'post_excerpt' => $optimizations['caption']
            ]);
        }
        
        if (isset($optimizations['description'])) {
            wp_update_post([
                'ID' => $image_id,
                'post_content' => $optimizations['description']
            ]);
        }
    }
    
    /**
     * Handle microservice job failure
     */
    private function handle_microservice_failure($job_id, $job, $error_message) {
        global $wpdb;
        
        $microservice_table = $wpdb->prefix . 'ufio_microservice_queue';
        
        if ($job['attempts'] >= $job['max_attempts']) {
            // Mark as failed
            $wpdb->update($microservice_table, [
                'status' => 'failed',
                'error_message' => $error_message
            ], ['id' => $job_id]);
        } else {
            // Reset to pending for retry
            $wpdb->update($microservice_table, [
                'status' => 'pending',
                'error_message' => $error_message
            ], ['id' => $job_id]);
        }
        
        // Log error
        $this->log_analytics('microservice_error', $job['post_id'] ?? null, $job_id, [
            'job_type' => $job['job_type'],
            'error' => $error_message,
            'attempts' => $job['attempts']
        ]);
    }
    
    /**
     * Calculate job priority based on analysis
     */
    private function calculate_job_priority($analysis) {
        $seo_potential = $analysis['seo_potential'] ?? 50;
        
        if ($seo_potential >= 80) return 1; // Highest priority
        if ($seo_potential >= 60) return 2; // High priority
        if ($seo_potential >= 40) return 3; // Medium priority
        if ($seo_potential >= 20) return 4; // Low priority
        return 5; // Lowest priority
    }
    
    /**
     * Generate authentication token
     */
    private function generate_auth_token($identifier) {
        $secret = wp_salt('auth');
        $timestamp = time();
        $data = $identifier . '|' . $timestamp;
        
        return base64_encode($data . '|' . hash_hmac('sha256', $data, $secret));
    }
    
    /**
     * Verify authentication token
     */
    public function verify_auth_token($token, $identifier) {
        $decoded = base64_decode($token);
        $parts = explode('|', $decoded);
        
        if (count($parts) !== 3) {
            return false;
        }
        
        list($token_id, $timestamp, $hash) = $parts;
        
        // Check if token is for correct identifier
        if ($token_id != $identifier) {
            return false;
        }
        
        // Check if token is not too old (1 hour)
        if (time() - $timestamp > 3600) {
            return false;
        }
        
        // Verify hash
        $secret = wp_salt('auth');
        $data = $token_id . '|' . $timestamp;
        $expected_hash = hash_hmac('sha256', $data, $secret);
        
        return hash_equals($expected_hash, $hash);
    }
    
    /**
     * Get microservice status
     */
    public function get_service_status() {
        if (empty($this->service_url)) {
            return ['status' => 'not_configured'];
        }
        
        try {
            $response = wp_remote_get($this->service_url . '/health', [
                'timeout' => 5,
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->api_key
                ]
            ]);
            
            if (is_wp_error($response)) {
                return ['status' => 'error', 'message' => $response->get_error_message()];
            }
            
            $status_code = wp_remote_retrieve_response_code($response);
            if ($status_code === 200) {
                $body = json_decode(wp_remote_retrieve_body($response), true);
                return ['status' => 'healthy', 'data' => $body];
            } else {
                return ['status' => 'error', 'message' => 'HTTP ' . $status_code];
            }
            
        } catch (Exception $e) {
            return ['status' => 'error', 'message' => $e->getMessage()];
        }
    }
    
    /**
     * Log analytics
     */
    private function log_analytics($event_type, $post_id = null, $job_id = null, $metadata = []) {
        global $wpdb;
        
        $analytics_table = $wpdb->prefix . 'ufio_analytics';
        
        $wpdb->insert($analytics_table, [
            'event_type' => $event_type,
            'post_id' => $post_id,
            'batch_id' => $job_id,
            'memory_usage_mb' => round(memory_get_usage(true) / 1024 / 1024, 2),
            'success' => strpos($event_type, 'error') === false,
            'metadata' => json_encode($metadata)
        ]);
    }
}
