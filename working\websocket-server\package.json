{"name": "ufio-websocket-server", "version": "1.0.0", "description": "Real-time WebSocket server for UFIO Command Center", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "lint": "eslint .", "docker:build": "docker build -t ufio-websocket .", "docker:run": "docker run -p 8080:8080 ufio-websocket"}, "dependencies": {"socket.io": "^4.7.4", "redis": "^4.6.10"}, "devDependencies": {"nodemon": "^3.0.2", "eslint": "^8.56.0", "jest": "^29.7.0"}, "engines": {"node": ">=16.0.0"}, "keywords": ["websocket", "real-time", "wordpress", "socket.io", "redis"], "author": "Elite WordPress Developer", "license": "GPL-2.0-or-later"}