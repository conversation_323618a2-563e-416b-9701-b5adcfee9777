/**
 * UFIO Command Center - Design System & Themes
 * WCAG-AAA compliant color system with dark/light modes
 */

/* CSS Custom Properties - Design Tokens */
:root {
  /* Typography Scale */
  --font-family-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */
  
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;
  
  /* Spacing Scale */
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px */
  --space-3: 0.75rem;   /* 12px */
  --space-4: 1rem;      /* 16px */
  --space-5: 1.25rem;   /* 20px */
  --space-6: 1.5rem;    /* 24px */
  --space-8: 2rem;      /* 32px */
  --space-10: 2.5rem;   /* 40px */
  --space-12: 3rem;     /* 48px */
  --space-16: 4rem;     /* 64px */
  --space-20: 5rem;     /* 80px */
  
  /* Border Radius */
  --radius-sm: 0.375rem;   /* 6px */
  --radius-base: 0.5rem;   /* 8px */
  --radius-md: 0.75rem;    /* 12px */
  --radius-lg: 1rem;       /* 16px */
  --radius-xl: 1.5rem;     /* 24px */
  --radius-full: 9999px;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Transitions */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-base: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Z-Index Scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* Light Theme - WCAG AAA Compliant */
.theme-light {
  /* Primary Colors */
  --color-primary-50: #f0f4ff;
  --color-primary-100: #e0e9ff;
  --color-primary-200: #c7d6fe;
  --color-primary-300: #a5b8fc;
  --color-primary-400: #8b93f8;
  --color-primary-500: #7c6df2;
  --color-primary-600: #6d4de6;
  --color-primary-700: #5b3bd1;
  --color-primary-800: #4a2fa7;
  --color-primary-900: #3d2785;
  
  /* Semantic Colors */
  --color-success-50: #f0fdf4;
  --color-success-500: #22c55e;
  --color-success-700: #15803d;
  
  --color-warning-50: #fffbeb;
  --color-warning-500: #f59e0b;
  --color-warning-700: #b45309;
  
  --color-error-50: #fef2f2;
  --color-error-500: #ef4444;
  --color-error-700: #b91c1c;
  
  --color-info-50: #eff6ff;
  --color-info-500: #3b82f6;
  --color-info-700: #1d4ed8;
  
  /* Neutral Colors */
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;
  
  /* Surface Colors */
  --color-background: #ffffff;
  --color-surface: #ffffff;
  --color-surface-variant: #f9fafb;
  --color-surface-hover: #f3f4f6;
  --color-surface-active: #e5e7eb;
  
  /* Text Colors - WCAG AAA (7:1 contrast) */
  --color-on-background: #111827;
  --color-on-surface: #111827;
  --color-on-surface-variant: #374151;
  --color-on-surface-muted: #6b7280;
  
  /* Border Colors */
  --color-outline: #d1d5db;
  --color-outline-variant: #e5e7eb;
  
  /* Interactive Colors */
  --color-primary: var(--color-primary-600);
  --color-on-primary: #ffffff;
  --color-primary-hover: var(--color-primary-700);
  --color-primary-active: var(--color-primary-800);
  
  --color-success: var(--color-success-700);
  --color-on-success: #ffffff;
  --color-warning: var(--color-warning-700);
  --color-on-warning: #ffffff;
  --color-error: var(--color-error-700);
  --color-on-error: #ffffff;
  --color-info: var(--color-info-700);
  --color-on-info: #ffffff;
}

/* Dark Theme - WCAG AAA Compliant */
.theme-dark {
  /* Primary Colors */
  --color-primary-50: #1e1b3a;
  --color-primary-100: #2d2a4a;
  --color-primary-200: #3d3a5a;
  --color-primary-300: #5d5a7a;
  --color-primary-400: #7d7a9a;
  --color-primary-500: #9d9aba;
  --color-primary-600: #bdbadd;
  --color-primary-700: #dddaff;
  --color-primary-800: #edeaff;
  --color-primary-900: #f7f4ff;
  
  /* Semantic Colors */
  --color-success-50: #0a2e0a;
  --color-success-500: #4ade80;
  --color-success-700: #86efac;
  
  --color-warning-50: #2e1a00;
  --color-warning-500: #fbbf24;
  --color-warning-700: #fcd34d;
  
  --color-error-50: #2e0a0a;
  --color-error-500: #f87171;
  --color-error-700: #fca5a5;
  
  --color-info-50: #0a1a2e;
  --color-info-500: #60a5fa;
  --color-info-700: #93c5fd;
  
  /* Neutral Colors */
  --color-gray-50: #0f1419;
  --color-gray-100: #1a1f2e;
  --color-gray-200: #252a3a;
  --color-gray-300: #30354a;
  --color-gray-400: #4a5568;
  --color-gray-500: #718096;
  --color-gray-600: #a0aec0;
  --color-gray-700: #cbd5e0;
  --color-gray-800: #e2e8f0;
  --color-gray-900: #f7fafc;
  
  /* Surface Colors */
  --color-background: #0f1419;
  --color-surface: #1a1f2e;
  --color-surface-variant: #252a3a;
  --color-surface-hover: #30354a;
  --color-surface-active: #3a404a;
  
  /* Text Colors - WCAG AAA (7:1 contrast) */
  --color-on-background: #f7fafc;
  --color-on-surface: #f7fafc;
  --color-on-surface-variant: #e2e8f0;
  --color-on-surface-muted: #a0aec0;
  
  /* Border Colors */
  --color-outline: #4a5568;
  --color-outline-variant: #30354a;
  
  /* Interactive Colors */
  --color-primary: var(--color-primary-400);
  --color-on-primary: #0f1419;
  --color-primary-hover: var(--color-primary-300);
  --color-primary-active: var(--color-primary-200);
  
  --color-success: var(--color-success-500);
  --color-on-success: #0f1419;
  --color-warning: var(--color-warning-500);
  --color-on-warning: #0f1419;
  --color-error: var(--color-error-500);
  --color-on-error: #0f1419;
  --color-info: var(--color-info-500);
  --color-on-info: #0f1419;
}

/* High Contrast Mode */
[data-high-contrast="true"] {
  --color-outline: currentColor;
  --shadow-base: 0 0 0 2px currentColor;
  --shadow-md: 0 0 0 3px currentColor;
  
  /* Increase border widths */
  --border-width: 2px;
}

[data-high-contrast="true"] .theme-light {
  --color-on-background: #000000;
  --color-on-surface: #000000;
  --color-background: #ffffff;
  --color-surface: #ffffff;
  --color-primary: #0000ff;
  --color-error: #ff0000;
  --color-success: #008000;
}

[data-high-contrast="true"] .theme-dark {
  --color-on-background: #ffffff;
  --color-on-surface: #ffffff;
  --color-background: #000000;
  --color-surface: #000000;
  --color-primary: #00ffff;
  --color-error: #ff6666;
  --color-success: #66ff66;
}

/* Reduced Motion */
[data-reduced-motion="true"] * {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
  scroll-behavior: auto !important;
}

/* Focus Styles - WCAG AAA */
[data-focus-visible="true"] {
  outline: 3px solid var(--color-primary);
  outline-offset: 2px;
  border-radius: var(--radius-sm);
}

/* Screen Reader Only */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Skip Links */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--color-primary);
  color: var(--color-on-primary);
  padding: 8px;
  text-decoration: none;
  border-radius: var(--radius-sm);
  z-index: var(--z-tooltip);
  font-weight: var(--font-weight-medium);
}

.skip-link:focus {
  top: 6px;
}

/* Font Size Adjustments */
[data-font-size="small"] {
  --font-size-base: 0.875rem;
}

[data-font-size="large"] {
  --font-size-base: 1.125rem;
}

[data-font-size="xlarge"] {
  --font-size-base: 1.25rem;
}
