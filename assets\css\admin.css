/**
 * AI SEO Optimizer Ultra - Admin Styles
 * 
 * Professional admin interface styles with modern design
 * 
 * @package AiSeoOptimizerUltra
 * @since 9.0.0
 */

/* ==========================================================================
   Variables and Base Styles
   ========================================================================== */

:root {
    --ai-seo-primary: #2271b1;
    --ai-seo-primary-hover: #135e96;
    --ai-seo-success: #00a32a;
    --ai-seo-warning: #dba617;
    --ai-seo-error: #d63638;
    --ai-seo-info: #72aee6;
    --ai-seo-border: #c3c4c7;
    --ai-seo-bg-light: #f6f7f7;
    --ai-seo-text: #1d2327;
    --ai-seo-text-light: #646970;
    --ai-seo-radius: 4px;
    --ai-seo-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    --ai-seo-transition: all 0.2s ease;
}

/* ==========================================================================
   Meta Box Styles
   ========================================================================== */

.ai-seo-ultra-meta-box {
    background: #fff;
    border: 1px solid var(--ai-seo-border);
    border-radius: var(--ai-seo-radius);
    padding: 20px;
    margin: 10px 0;
}

.ai-seo-ultra-meta-box h3 {
    margin: 0 0 15px 0;
    padding: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--ai-seo-text);
    border-bottom: 1px solid var(--ai-seo-border);
    padding-bottom: 10px;
}

.ai-seo-field {
    margin-bottom: 20px;
}

.ai-seo-field:last-child {
    margin-bottom: 0;
}

.ai-seo-field label {
    display: block;
    font-weight: 600;
    margin-bottom: 5px;
    color: var(--ai-seo-text);
}

.ai-seo-field input[type="text"],
.ai-seo-field input[type="url"],
.ai-seo-field textarea,
.ai-seo-field select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--ai-seo-border);
    border-radius: var(--ai-seo-radius);
    font-size: 14px;
    transition: var(--ai-seo-transition);
    background: #fff;
}

.ai-seo-field input:focus,
.ai-seo-field textarea:focus,
.ai-seo-field select:focus {
    border-color: var(--ai-seo-primary);
    box-shadow: 0 0 0 1px var(--ai-seo-primary);
    outline: none;
}

.ai-seo-field textarea {
    min-height: 80px;
    resize: vertical;
}

.ai-seo-field-description {
    font-size: 13px;
    color: var(--ai-seo-text-light);
    margin-top: 5px;
    line-height: 1.4;
}

/* ==========================================================================
   Score Display
   ========================================================================== */

.ai-seo-scores {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin: 20px 0;
}

.ai-seo-score-item {
    background: var(--ai-seo-bg-light);
    border: 1px solid var(--ai-seo-border);
    border-radius: var(--ai-seo-radius);
    padding: 15px;
    text-align: center;
    transition: var(--ai-seo-transition);
}

.ai-seo-score-item:hover {
    box-shadow: var(--ai-seo-shadow);
}

.ai-seo-score-label {
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: var(--ai-seo-text-light);
    margin-bottom: 8px;
}

.ai-seo-score-value {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 5px;
}

.ai-seo-score-bar {
    height: 4px;
    background: #e0e0e0;
    border-radius: 2px;
    overflow: hidden;
    margin-top: 8px;
}

.ai-seo-score-fill {
    height: 100%;
    transition: width 0.3s ease;
    border-radius: 2px;
}

/* Score Colors */
.score-poor .ai-seo-score-value,
.score-poor .ai-seo-score-fill {
    color: var(--ai-seo-error);
    background-color: var(--ai-seo-error);
}

.score-fair .ai-seo-score-value,
.score-fair .ai-seo-score-fill {
    color: var(--ai-seo-warning);
    background-color: var(--ai-seo-warning);
}

.score-good .ai-seo-score-value,
.score-good .ai-seo-score-fill {
    color: var(--ai-seo-info);
    background-color: var(--ai-seo-info);
}

.score-excellent .ai-seo-score-value,
.score-excellent .ai-seo-score-fill {
    color: var(--ai-seo-success);
    background-color: var(--ai-seo-success);
}

/* ==========================================================================
   Buttons and Actions
   ========================================================================== */

.ai-seo-button {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: var(--ai-seo-primary);
    color: #fff;
    border: none;
    border-radius: var(--ai-seo-radius);
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--ai-seo-transition);
    line-height: 1.4;
}

.ai-seo-button:hover,
.ai-seo-button:focus {
    background: var(--ai-seo-primary-hover);
    color: #fff;
    text-decoration: none;
}

.ai-seo-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.ai-seo-button.secondary {
    background: #fff;
    color: var(--ai-seo-primary);
    border: 1px solid var(--ai-seo-primary);
}

.ai-seo-button.secondary:hover {
    background: var(--ai-seo-primary);
    color: #fff;
}

.ai-seo-button.small {
    padding: 6px 12px;
    font-size: 13px;
}

.ai-seo-button.large {
    padding: 12px 24px;
    font-size: 16px;
}

.ai-seo-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid var(--ai-seo-border);
}

/* ==========================================================================
   Loading and Status
   ========================================================================== */

.ai-seo-loading {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    color: var(--ai-seo-text-light);
    font-size: 14px;
}

.ai-seo-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid #e0e0e0;
    border-top: 2px solid var(--ai-seo-primary);
    border-radius: 50%;
    animation: ai-seo-spin 1s linear infinite;
}

@keyframes ai-seo-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.ai-seo-status {
    padding: 10px 15px;
    border-radius: var(--ai-seo-radius);
    font-size: 14px;
    margin: 10px 0;
}

.ai-seo-status.success {
    background: #d1e7dd;
    color: #0f5132;
    border: 1px solid #badbcc;
}

.ai-seo-status.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c2c7;
}

.ai-seo-status.warning {
    background: #fff3cd;
    color: #664d03;
    border: 1px solid #ffecb5;
}

.ai-seo-status.info {
    background: #d1ecf1;
    color: #055160;
    border: 1px solid #b6d4ea;
}

/* ==========================================================================
   Suggestions Display
   ========================================================================== */

.ai-seo-suggestions {
    background: #fff;
    border: 1px solid var(--ai-seo-border);
    border-radius: var(--ai-seo-radius);
    margin: 15px 0;
}

.ai-seo-suggestion-item {
    padding: 15px;
    border-bottom: 1px solid var(--ai-seo-border);
}

.ai-seo-suggestion-item:last-child {
    border-bottom: none;
}

.ai-seo-suggestion-label {
    font-weight: 600;
    color: var(--ai-seo-text);
    margin-bottom: 8px;
    display: block;
}

.ai-seo-suggestion-content {
    background: var(--ai-seo-bg-light);
    padding: 10px;
    border-radius: var(--ai-seo-radius);
    font-family: monospace;
    font-size: 13px;
    line-height: 1.4;
    margin-bottom: 10px;
}

.ai-seo-suggestion-actions {
    display: flex;
    gap: 8px;
}

/* ==========================================================================
   Responsive Design
   ========================================================================== */

@media (max-width: 782px) {
    .ai-seo-scores {
        grid-template-columns: 1fr;
    }
    
    .ai-seo-actions {
        flex-direction: column;
    }
    
    .ai-seo-button {
        justify-content: center;
    }
}

/* ==========================================================================
   WordPress Admin Integration
   ========================================================================== */

.postbox .ai-seo-ultra-meta-box {
    border: none;
    margin: 0;
    padding: 0;
}

.postbox .ai-seo-ultra-meta-box h3 {
    display: none; /* WordPress provides the title */
}

/* ==========================================================================
   Utilities
   ========================================================================== */

.ai-seo-hidden {
    display: none !important;
}

.ai-seo-text-center {
    text-align: center;
}

.ai-seo-text-right {
    text-align: right;
}

.ai-seo-mb-0 {
    margin-bottom: 0 !important;
}

.ai-seo-mt-15 {
    margin-top: 15px;
}

.ai-seo-p-relative {
    position: relative;
}

/* ==========================================================================
   Dark Mode Support
   ========================================================================== */

@media (prefers-color-scheme: dark) {
    .ai-seo-ultra-meta-box {
        background: #1d2327;
        border-color: #3c434a;
    }
    
    .ai-seo-field input,
    .ai-seo-field textarea,
    .ai-seo-field select {
        background: #1d2327;
        border-color: #3c434a;
        color: #f0f0f1;
    }
    
    .ai-seo-suggestion-content {
        background: #2c3338;
        color: #f0f0f1;
    }
}
