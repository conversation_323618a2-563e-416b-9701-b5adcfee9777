<?php
/**
 * UFIO Database Schema and Migrations
 * Handles all database operations for the optimized plugin
 */

if (!defined('ABSPATH')) {
    exit;
}

class UFIO_Database {
    
    private static $instance = null;
    private $version = '2.0.0';
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        add_action('init', [$this, 'maybe_upgrade']);
    }
    
    /**
     * Create all required tables
     */
    public function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Priority-based processing queue
        $queue_table = $wpdb->prefix . 'ufio_queue';
        $queue_sql = "CREATE TABLE $queue_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            post_id bigint(20) unsigned NOT NULL,
            priority_score decimal(10,4) NOT NULL DEFAULT 0.0000,
            traffic_weight decimal(8,4) NOT NULL DEFAULT 0.0000,
            seo_potential decimal(8,4) NOT NULL DEFAULT 0.0000,
            status enum('pending','processing','completed','failed','paused') NOT NULL DEFAULT 'pending',
            attempts tinyint(3) unsigned NOT NULL DEFAULT 0,
            max_attempts tinyint(3) unsigned NOT NULL DEFAULT 3,
            locked_until datetime DEFAULT NULL,
            locked_by varchar(32) DEFAULT NULL,
            error_message text DEFAULT NULL,
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            completed_at datetime DEFAULT NULL,
            PRIMARY KEY (id),
            UNIQUE KEY post_id (post_id),
            KEY priority_score (priority_score DESC),
            KEY status (status),
            KEY locked_until (locked_until),
            KEY created_at (created_at)
        ) $charset_collate;";
        
        // Batch processing cache
        $batch_cache_table = $wpdb->prefix . 'ufio_batch_cache';
        $batch_cache_sql = "CREATE TABLE $batch_cache_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            cache_key varchar(32) NOT NULL,
            post_ids text NOT NULL,
            content_hash varchar(32) NOT NULL,
            ai_response longtext NOT NULL,
            expires_at datetime NOT NULL,
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY cache_key (cache_key),
            KEY expires_at (expires_at),
            KEY content_hash (content_hash)
        ) $charset_collate;";
        
        // Image embeddings and metadata cache
        $image_cache_table = $wpdb->prefix . 'ufio_image_cache';
        $image_cache_sql = "CREATE TABLE $image_cache_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            attachment_id bigint(20) unsigned NOT NULL,
            clip_embedding longtext DEFAULT NULL,
            keywords_json text DEFAULT NULL,
            alt_text_optimized varchar(255) DEFAULT NULL,
            title_optimized varchar(255) DEFAULT NULL,
            seo_score decimal(5,2) DEFAULT NULL,
            file_hash varchar(32) DEFAULT NULL,
            last_analyzed datetime DEFAULT NULL,
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY attachment_id (attachment_id),
            KEY file_hash (file_hash),
            KEY seo_score (seo_score DESC),
            KEY last_analyzed (last_analyzed)
        ) $charset_collate;";
        
        // Processing analytics and performance metrics
        $analytics_table = $wpdb->prefix . 'ufio_analytics';
        $analytics_sql = "CREATE TABLE $analytics_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            event_type enum('batch_process','single_process','api_call','cache_hit','cache_miss','error') NOT NULL,
            post_id bigint(20) unsigned DEFAULT NULL,
            batch_id varchar(32) DEFAULT NULL,
            processing_time_ms int(10) unsigned DEFAULT NULL,
            api_tokens_used int(10) unsigned DEFAULT NULL,
            api_cost_usd decimal(10,6) DEFAULT NULL,
            memory_usage_mb decimal(8,2) DEFAULT NULL,
            success boolean NOT NULL DEFAULT true,
            error_code varchar(50) DEFAULT NULL,
            metadata json DEFAULT NULL,
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY event_type (event_type),
            KEY post_id (post_id),
            KEY batch_id (batch_id),
            KEY created_at (created_at),
            KEY success (success)
        ) $charset_collate;";
        
        // Microservice job queue
        $microservice_queue_table = $wpdb->prefix . 'ufio_microservice_queue';
        $microservice_queue_sql = "CREATE TABLE $microservice_queue_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            job_type enum('image_search','embedding_generation','seo_analysis') NOT NULL,
            post_id bigint(20) unsigned DEFAULT NULL,
            attachment_id bigint(20) unsigned DEFAULT NULL,
            payload json NOT NULL,
            status enum('pending','processing','completed','failed') NOT NULL DEFAULT 'pending',
            priority tinyint(3) unsigned NOT NULL DEFAULT 5,
            attempts tinyint(3) unsigned NOT NULL DEFAULT 0,
            max_attempts tinyint(3) unsigned NOT NULL DEFAULT 3,
            response json DEFAULT NULL,
            error_message text DEFAULT NULL,
            processing_time_ms int(10) unsigned DEFAULT NULL,
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            completed_at datetime DEFAULT NULL,
            PRIMARY KEY (id),
            KEY job_type (job_type),
            KEY status (status),
            KEY priority (priority DESC),
            KEY post_id (post_id),
            KEY attachment_id (attachment_id),
            KEY created_at (created_at)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        
        dbDelta($queue_sql);
        dbDelta($batch_cache_sql);
        dbDelta($image_cache_sql);
        dbDelta($analytics_sql);
        dbDelta($microservice_queue_sql);
        
        // Create indexes for performance
        $this->create_performance_indexes();
        
        update_option('ufio_db_version', $this->version);
    }
    
    /**
     * Create additional performance indexes
     */
    private function create_performance_indexes() {
        global $wpdb;
        
        $queue_table = $wpdb->prefix . 'ufio_queue';
        $analytics_table = $wpdb->prefix . 'ufio_analytics';
        
        // Composite indexes for queue optimization
        $wpdb->query("CREATE INDEX IF NOT EXISTS idx_queue_priority_status ON $queue_table (status, priority_score DESC, created_at)");
        $wpdb->query("CREATE INDEX IF NOT EXISTS idx_queue_processing ON $queue_table (status, locked_until, attempts)");
        
        // Analytics performance indexes
        $wpdb->query("CREATE INDEX IF NOT EXISTS idx_analytics_performance ON $analytics_table (event_type, created_at DESC)");
        $wpdb->query("CREATE INDEX IF NOT EXISTS idx_analytics_costs ON $analytics_table (api_cost_usd, created_at DESC)");
    }
    
    /**
     * Check if upgrade is needed
     */
    public function maybe_upgrade() {
        $current_version = get_option('ufio_db_version', '0.0.0');
        
        if (version_compare($current_version, $this->version, '<')) {
            $this->create_tables();
        }
    }
    
    /**
     * Get queue statistics
     */
    public function get_queue_stats() {
        global $wpdb;
        
        $queue_table = $wpdb->prefix . 'ufio_queue';
        
        $stats = $wpdb->get_row("
            SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                SUM(CASE WHEN status = 'processing' THEN 1 ELSE 0 END) as processing,
                SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
                SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed,
                AVG(priority_score) as avg_priority,
                MAX(priority_score) as max_priority
            FROM $queue_table
        ", ARRAY_A);
        
        return $stats ?: [
            'total' => 0,
            'pending' => 0,
            'processing' => 0,
            'completed' => 0,
            'failed' => 0,
            'avg_priority' => 0,
            'max_priority' => 0
        ];
    }
    
    /**
     * Get performance analytics
     */
    public function get_performance_analytics($days = 7) {
        global $wpdb;
        
        $analytics_table = $wpdb->prefix . 'ufio_analytics';
        $since = date('Y-m-d H:i:s', strtotime("-$days days"));
        
        $performance = $wpdb->get_row($wpdb->prepare("
            SELECT 
                COUNT(*) as total_events,
                AVG(processing_time_ms) as avg_processing_time,
                SUM(api_tokens_used) as total_tokens,
                SUM(api_cost_usd) as total_cost,
                AVG(memory_usage_mb) as avg_memory,
                SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) / COUNT(*) * 100 as success_rate
            FROM $analytics_table 
            WHERE created_at >= %s
        ", $since), ARRAY_A);
        
        return $performance ?: [
            'total_events' => 0,
            'avg_processing_time' => 0,
            'total_tokens' => 0,
            'total_cost' => 0,
            'avg_memory' => 0,
            'success_rate' => 0
        ];
    }
    
    /**
     * Clean up old data
     */
    public function cleanup_old_data() {
        global $wpdb;
        
        // Clean expired batch cache
        $batch_cache_table = $wpdb->prefix . 'ufio_batch_cache';
        $wpdb->query("DELETE FROM $batch_cache_table WHERE expires_at < NOW()");
        
        // Clean old analytics (keep 30 days)
        $analytics_table = $wpdb->prefix . 'ufio_analytics';
        $wpdb->query("DELETE FROM $analytics_table WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)");
        
        // Clean completed queue items older than 7 days
        $queue_table = $wpdb->prefix . 'ufio_queue';
        $wpdb->query("DELETE FROM $queue_table WHERE status = 'completed' AND completed_at < DATE_SUB(NOW(), INTERVAL 7 DAY)");
    }
}
