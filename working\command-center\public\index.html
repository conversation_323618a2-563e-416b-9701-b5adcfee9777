<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#7c6df2" />
    <meta name="description" content="UFIO Command Center - Ultra Featured Image Optimizer Pro" />
    
    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://api.openai.com">
    
    <!-- Critical CSS will be inlined by WordPress -->
    
    <title>UFIO Command Center</title>
    
    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🎛️</text></svg>">
    
    <!-- Manifest for PWA -->
    <link rel="manifest" href="./manifest.json" />
    
    <!-- Apple Touch Icon -->
    <link rel="apple-touch-icon" href="./icon-192x192.png" />
    
    <!-- Performance hints -->
    <link rel="dns-prefetch" href="//fonts.googleapis.com">
    <link rel="dns-prefetch" href="//api.openai.com">
    
    <!-- Critical resource hints -->
    <link rel="modulepreload" href="./static/js/main.js">
    <link rel="preload" href="./static/css/main.css" as="style">
    
    <!-- Security headers -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    
    <!-- Open Graph -->
    <meta property="og:title" content="UFIO Command Center">
    <meta property="og:description" content="Ultra Featured Image Optimizer Pro - AI-powered image optimization">
    <meta property="og:type" content="website">
    <meta property="og:image" content="./og-image.png">
    
    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="UFIO Command Center">
    <meta name="twitter:description" content="Ultra Featured Image Optimizer Pro - AI-powered image optimization">
    <meta name="twitter:image" content="./og-image.png">
</head>
<body>
    <noscript>
        <div style="
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            text-align: center;
            padding: 2rem;
            background: #f9fafb;
        ">
            <div>
                <h1 style="color: #1f2937; margin-bottom: 1rem;">JavaScript Required</h1>
                <p style="color: #6b7280; margin-bottom: 2rem;">
                    UFIO Command Center requires JavaScript to function properly.<br>
                    Please enable JavaScript in your browser and refresh the page.
                </p>
                <button onclick="window.location.reload()" style="
                    background: #7c6df2;
                    color: white;
                    border: none;
                    padding: 0.75rem 1.5rem;
                    border-radius: 0.5rem;
                    font-weight: 600;
                    cursor: pointer;
                ">
                    Refresh Page
                </button>
            </div>
        </div>
    </noscript>
    
    <!-- Main app container -->
    <div id="ufio-command-center">
        <!-- Loading screen -->
        <div id="loading-screen" style="
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            flex-direction: column;
            gap: 2rem;
        ">
            <div style="text-align: center;">
                <div style="
                    width: 60px;
                    height: 60px;
                    border: 4px solid rgba(255, 255, 255, 0.3);
                    border-top: 4px solid white;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                    margin: 0 auto 1rem;
                "></div>
                <h1 style="margin: 0 0 0.5rem; font-size: 2rem; font-weight: 700;">
                    🎛️ UFIO Command Center
                </h1>
                <p style="margin: 0; opacity: 0.9; font-size: 1.1rem;">
                    Initializing ultra-high-performance dashboard...
                </p>
            </div>
            
            <div style="
                background: rgba(255, 255, 255, 0.1);
                padding: 1rem 2rem;
                border-radius: 12px;
                backdrop-filter: blur(10px);
                text-align: center;
                max-width: 500px;
            ">
                <p style="margin: 0 0 0.5rem; font-weight: 600;">Features Loading:</p>
                <ul style="
                    list-style: none;
                    padding: 0;
                    margin: 0;
                    font-size: 0.9rem;
                    opacity: 0.9;
                ">
                    <li>✨ Real-time heat map visualization</li>
                    <li>⚡ Virtualized tables for 100k+ posts</li>
                    <li>🔄 WebSocket live updates</li>
                    <li>🎨 Dark/light theme with WCAG-AAA</li>
                    <li>🚀 <100ms interaction latency</li>
                </ul>
            </div>
        </div>
    </div>
    
    <!-- Critical CSS for loading animation -->
    <style>
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        
        #ufio-command-center {
            min-height: 100vh;
        }
        
        /* Hide loading screen once React loads */
        .react-loaded #loading-screen {
            display: none !important;
        }
    </style>
    
    <!-- Service Worker Registration -->
    <script>
        if ('serviceWorker' in navigator && window.location.protocol === 'https:') {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('./sw.js')
                    .then(function(registration) {
                        console.log('SW registered: ', registration);
                    })
                    .catch(function(registrationError) {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }
    </script>
    
    <!-- Performance monitoring -->
    <script>
        // Web Vitals monitoring
        window.ufioPerformance = {
            startTime: performance.now(),
            marks: {},
            
            mark: function(name) {
                this.marks[name] = performance.now();
                performance.mark('ufio-' + name);
            },
            
            measure: function(name, start, end) {
                const startTime = this.marks[start] || 0;
                const endTime = this.marks[end] || performance.now();
                const duration = endTime - startTime;
                
                performance.measure('ufio-' + name, 'ufio-' + start, 'ufio-' + end);
                
                return duration;
            }
        };
        
        // Mark initial load
        window.ufioPerformance.mark('html-loaded');
        
        // Track when React app loads
        window.addEventListener('DOMContentLoaded', function() {
            window.ufioPerformance.mark('dom-ready');
        });
    </script>
</body>
</html>
