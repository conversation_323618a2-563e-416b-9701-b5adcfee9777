import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { FixedSizeList as List } from 'react-window';
import InfiniteLoader from 'react-window-infinite-loader';
import AutoSizer from 'react-virtualized-auto-sizer';
import { motion } from 'framer-motion';
import { useQuery, useInfiniteQuery } from 'react-query';
import { useWebSocket } from '@hooks/useWebSocket';
import { useAccessibility } from '@hooks/useAccessibility';
import { HeatMapFilters } from './HeatMapFilters';
import { HeatMapLegend } from './HeatMapLegend';
import { PostHeatMapRow } from './PostHeatMapRow';
import { LoadingSpinner } from './LoadingSpinner';
import styles from './PostsHeatMap.module.css';

const ITEM_HEIGHT = 80;
const ITEMS_PER_PAGE = 100;

// Heat map color calculation
const getHeatColor = (traffic, seo, hasFeaturedImage) => {
  if (hasFeaturedImage) return 'var(--ufio-success-light)';
  
  const intensity = (traffic * 0.6 + seo * 0.4) / 100;
  
  if (intensity >= 0.8) return 'var(--ufio-error)'; // Critical - high traffic, no image
  if (intensity >= 0.6) return 'var(--ufio-warning)'; // High priority
  if (intensity >= 0.4) return 'var(--ufio-warning-light)'; // Medium priority
  if (intensity >= 0.2) return 'var(--ufio-info-light)'; // Low priority
  return 'var(--ufio-surface-variant)'; // Very low priority
};

export default function PostsHeatMap() {
  const [filters, setFilters] = useState({
    search: '',
    postType: 'all',
    trafficRange: [0, 100],
    seoRange: [0, 100],
    featuredImageStatus: 'all',
    sortBy: 'priority',
    sortOrder: 'desc'
  });
  
  const [selectedPosts, setSelectedPosts] = useState(new Set());
  const { reducedMotion } = useAccessibility();
  const { sendMessage } = useWebSocket();
  
  // Infinite query for posts data
  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    error
  } = useInfiniteQuery(
    ['posts-heatmap', filters],
    async ({ pageParam = 0 }) => {
      const params = new URLSearchParams({
        page: pageParam,
        per_page: ITEMS_PER_PAGE,
        ...filters
      });
      
      const response = await fetch(
        `${window.UFIOCommandCenter.apiUrl}ufio/v1/posts/heatmap?${params}`,
        {
          headers: {
            'X-WP-Nonce': window.UFIOCommandCenter.nonce
          }
        }
      );
      
      if (!response.ok) {
        throw new Error('Failed to fetch posts data');
      }
      
      return response.json();
    },
    {
      getNextPageParam: (lastPage, pages) => {
        return lastPage.hasMore ? pages.length : undefined;
      },
      staleTime: 30000, // 30 seconds
      refetchInterval: 60000 // 1 minute
    }
  );
  
  // Flatten all pages into single array
  const posts = useMemo(() => {
    return data?.pages?.flatMap(page => page.posts) || [];
  }, [data]);
  
  // Statistics
  const stats = useMemo(() => {
    const total = posts.length;
    const withoutFeatured = posts.filter(p => !p.hasFeaturedImage).length;
    const highPriority = posts.filter(p => p.priorityScore >= 80).length;
    const avgTraffic = posts.reduce((sum, p) => sum + p.trafficWeight, 0) / total || 0;
    const avgSeo = posts.reduce((sum, p) => sum + p.seoScore, 0) / total || 0;
    
    return {
      total,
      withoutFeatured,
      highPriority,
      avgTraffic: Math.round(avgTraffic),
      avgSeo: Math.round(avgSeo),
      coverageRate: Math.round(((total - withoutFeatured) / total) * 100) || 0
    };
  }, [posts]);
  
  // Check if item is loaded
  const isItemLoaded = useCallback((index) => {
    return !!posts[index];
  }, [posts]);
  
  // Load more items
  const loadMoreItems = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage) {
      return fetchNextPage();
    }
    return Promise.resolve();
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);
  
  // Handle post selection
  const handlePostSelect = useCallback((postId, selected) => {
    setSelectedPosts(prev => {
      const newSet = new Set(prev);
      if (selected) {
        newSet.add(postId);
      } else {
        newSet.delete(postId);
      }
      return newSet;
    });
  }, []);
  
  // Handle bulk actions
  const handleBulkAction = useCallback(async (action) => {
    if (selectedPosts.size === 0) return;
    
    const postIds = Array.from(selectedPosts);
    
    try {
      switch (action) {
        case 'assign-featured':
          sendMessage({
            type: 'bulk_assign_featured',
            data: { postIds }
          });
          break;
        case 'add-to-queue':
          sendMessage({
            type: 'bulk_add_to_queue',
            data: { postIds, priority: 8 }
          });
          break;
        case 'analyze':
          sendMessage({
            type: 'bulk_analyze',
            data: { postIds }
          });
          break;
        default:
          break;
      }
      
      setSelectedPosts(new Set());
    } catch (error) {
      console.error('Bulk action failed:', error);
    }
  }, [selectedPosts, sendMessage]);
  
  // Row renderer for virtualized list
  const Row = useCallback(({ index, style }) => {
    const post = posts[index];
    
    if (!post) {
      return (
        <div style={style} className={styles.loadingRow}>
          <LoadingSpinner size="small" />
        </div>
      );
    }
    
    const heatColor = getHeatColor(post.trafficWeight, post.seoScore, post.hasFeaturedImage);
    
    return (
      <div style={style}>
        <PostHeatMapRow
          post={post}
          heatColor={heatColor}
          isSelected={selectedPosts.has(post.id)}
          onSelect={handlePostSelect}
          reducedMotion={reducedMotion}
        />
      </div>
    );
  }, [posts, selectedPosts, handlePostSelect, reducedMotion]);
  
  if (isLoading) {
    return (
      <div className={styles.loading}>
        <LoadingSpinner size="large" />
        <p>Loading heat map data...</p>
      </div>
    );
  }
  
  if (error) {
    return (
      <div className={styles.error}>
        <h3>Failed to load heat map</h3>
        <p>{error.message}</p>
        <button onClick={() => window.location.reload()}>Retry</button>
      </div>
    );
  }
  
  const itemCount = hasNextPage ? posts.length + 1 : posts.length;
  
  return (
    <div className={styles.heatMapContainer}>
      {/* Header with stats */}
      <div className={styles.header}>
        <div className={styles.title}>
          <h1>🔥 Traffic × SEO Heat Map</h1>
          <p>Visual priority matrix for featured image optimization</p>
        </div>
        
        <div className={styles.stats}>
          <div className={styles.statCard}>
            <span className={styles.statValue}>{stats.total.toLocaleString()}</span>
            <span className={styles.statLabel}>Total Posts</span>
          </div>
          <div className={styles.statCard}>
            <span className={styles.statValue}>{stats.withoutFeatured.toLocaleString()}</span>
            <span className={styles.statLabel}>Missing Featured</span>
          </div>
          <div className={styles.statCard}>
            <span className={styles.statValue}>{stats.highPriority.toLocaleString()}</span>
            <span className={styles.statLabel}>High Priority</span>
          </div>
          <div className={styles.statCard}>
            <span className={styles.statValue}>{stats.coverageRate}%</span>
            <span className={styles.statLabel}>Coverage Rate</span>
          </div>
        </div>
      </div>
      
      {/* Filters and controls */}
      <div className={styles.controls}>
        <HeatMapFilters
          filters={filters}
          onFiltersChange={setFilters}
          selectedCount={selectedPosts.size}
          onBulkAction={handleBulkAction}
        />
        
        <HeatMapLegend />
      </div>
      
      {/* Virtualized list */}
      <div className={styles.listContainer}>
        <AutoSizer>
          {({ height, width }) => (
            <InfiniteLoader
              isItemLoaded={isItemLoaded}
              itemCount={itemCount}
              loadMoreItems={loadMoreItems}
            >
              {({ onItemsRendered, ref }) => (
                <List
                  ref={ref}
                  height={height}
                  width={width}
                  itemCount={itemCount}
                  itemSize={ITEM_HEIGHT}
                  onItemsRendered={onItemsRendered}
                  overscanCount={5}
                  className={styles.virtualList}
                >
                  {Row}
                </List>
              )}
            </InfiniteLoader>
          )}
        </AutoSizer>
      </div>
      
      {/* Selection info */}
      {selectedPosts.size > 0 && (
        <motion.div
          className={styles.selectionInfo}
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 50 }}
        >
          <span>{selectedPosts.size} posts selected</span>
          <button onClick={() => setSelectedPosts(new Set())}>
            Clear selection
          </button>
        </motion.div>
      )}
    </div>
  );
}
