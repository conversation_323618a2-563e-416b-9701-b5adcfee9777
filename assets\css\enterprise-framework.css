/**
 * 🚀 UFIO ENTERPRISE PRO - REVOLUTIONARY CSS FRAMEWORK
 * 
 * Ultra-modern, professional-grade design system with quantum performance optimization
 * Built for enterprise-level WordPress plugins with advanced UI/UX patterns
 * 
 * @package UFIO_Enterprise_Pro
 * @version 10.0.0
 * <AUTHOR> Enterprise Design Team
 * @copyright 2024 UFIO Enterprise Solutions
 */

/* ===== ENTERPRISE DESIGN TOKENS ===== */
:root {
  /* === QUANTUM COLOR PALETTE === */
  /* Primary Enterprise Colors */
  --ufio-primary: #0066ff;
  --ufio-primary-50: #e6f2ff;
  --ufio-primary-100: #b3d9ff;
  --ufio-primary-200: #80c0ff;
  --ufio-primary-300: #4da6ff;
  --ufio-primary-400: #1a8cff;
  --ufio-primary-500: #0066ff;
  --ufio-primary-600: #0052cc;
  --ufio-primary-700: #003d99;
  --ufio-primary-800: #002966;
  --ufio-primary-900: #001433;

  /* Success & Growth Colors */
  --ufio-success: #00d084;
  --ufio-success-50: #e6faf4;
  --ufio-success-100: #b3f0d9;
  --ufio-success-200: #80e6be;
  --ufio-success-300: #4ddca3;
  --ufio-success-400: #1ad288;
  --ufio-success-500: #00d084;
  --ufio-success-600: #00a66a;
  --ufio-success-700: #007d4f;
  --ufio-success-800: #005335;
  --ufio-success-900: #002a1a;

  /* Warning & Attention Colors */
  --ufio-warning: #ff9500;
  --ufio-warning-50: #fff4e6;
  --ufio-warning-100: #ffe0b3;
  --ufio-warning-200: #ffcc80;
  --ufio-warning-300: #ffb84d;
  --ufio-warning-400: #ffa41a;
  --ufio-warning-500: #ff9500;
  --ufio-warning-600: #cc7700;
  --ufio-warning-700: #995900;
  --ufio-warning-800: #663b00;
  --ufio-warning-900: #331e00;

  /* Error & Critical Colors */
  --ufio-error: #ff3366;
  --ufio-error-50: #ffe6ec;
  --ufio-error-100: #ffb3c4;
  --ufio-error-200: #ff809c;
  --ufio-error-300: #ff4d74;
  --ufio-error-400: #ff1a4c;
  --ufio-error-500: #ff3366;
  --ufio-error-600: #cc2952;
  --ufio-error-700: #991f3d;
  --ufio-error-800: #661529;
  --ufio-error-900: #330a14;

  /* Neutral Enterprise Grays */
  --ufio-gray-50: #f8fafc;
  --ufio-gray-100: #f1f5f9;
  --ufio-gray-200: #e2e8f0;
  --ufio-gray-300: #cbd5e1;
  --ufio-gray-400: #94a3b8;
  --ufio-gray-500: #64748b;
  --ufio-gray-600: #475569;
  --ufio-gray-700: #334155;
  --ufio-gray-800: #1e293b;
  --ufio-gray-900: #0f172a;

  /* === QUANTUM GRADIENTS === */
  --ufio-gradient-primary: linear-gradient(135deg, var(--ufio-primary-400) 0%, var(--ufio-primary-600) 100%);
  --ufio-gradient-success: linear-gradient(135deg, var(--ufio-success-400) 0%, var(--ufio-success-600) 100%);
  --ufio-gradient-warning: linear-gradient(135deg, var(--ufio-warning-400) 0%, var(--ufio-warning-600) 100%);
  --ufio-gradient-premium: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --ufio-gradient-quantum: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
  --ufio-gradient-enterprise: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);

  /* === ENTERPRISE TYPOGRAPHY === */
  --ufio-font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --ufio-font-family-mono: 'JetBrains Mono', 'Fira Code', 'Monaco', 'Consolas', monospace;
  
  /* Font Sizes - Fluid Typography */
  --ufio-text-xs: clamp(0.75rem, 0.7rem + 0.25vw, 0.875rem);
  --ufio-text-sm: clamp(0.875rem, 0.8rem + 0.375vw, 1rem);
  --ufio-text-base: clamp(1rem, 0.9rem + 0.5vw, 1.125rem);
  --ufio-text-lg: clamp(1.125rem, 1rem + 0.625vw, 1.25rem);
  --ufio-text-xl: clamp(1.25rem, 1.1rem + 0.75vw, 1.5rem);
  --ufio-text-2xl: clamp(1.5rem, 1.3rem + 1vw, 1.875rem);
  --ufio-text-3xl: clamp(1.875rem, 1.6rem + 1.375vw, 2.25rem);
  --ufio-text-4xl: clamp(2.25rem, 1.9rem + 1.75vw, 3rem);
  --ufio-text-5xl: clamp(3rem, 2.5rem + 2.5vw, 4rem);

  /* Font Weights */
  --ufio-font-weight-light: 300;
  --ufio-font-weight-normal: 400;
  --ufio-font-weight-medium: 500;
  --ufio-font-weight-semibold: 600;
  --ufio-font-weight-bold: 700;
  --ufio-font-weight-extrabold: 800;

  /* === QUANTUM SPACING SYSTEM === */
  --ufio-space-0: 0;
  --ufio-space-1: 0.25rem;
  --ufio-space-2: 0.5rem;
  --ufio-space-3: 0.75rem;
  --ufio-space-4: 1rem;
  --ufio-space-5: 1.25rem;
  --ufio-space-6: 1.5rem;
  --ufio-space-8: 2rem;
  --ufio-space-10: 2.5rem;
  --ufio-space-12: 3rem;
  --ufio-space-16: 4rem;
  --ufio-space-20: 5rem;
  --ufio-space-24: 6rem;
  --ufio-space-32: 8rem;

  /* === ENTERPRISE SHADOWS === */
  --ufio-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --ufio-shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --ufio-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --ufio-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --ufio-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --ufio-shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --ufio-shadow-quantum: 0 0 50px rgba(102, 126, 234, 0.3);
  --ufio-shadow-enterprise: 0 0 100px rgba(0, 102, 255, 0.2);

  /* === BORDER RADIUS === */
  --ufio-radius-none: 0;
  --ufio-radius-sm: 0.125rem;
  --ufio-radius-base: 0.25rem;
  --ufio-radius-md: 0.375rem;
  --ufio-radius-lg: 0.5rem;
  --ufio-radius-xl: 0.75rem;
  --ufio-radius-2xl: 1rem;
  --ufio-radius-3xl: 1.5rem;
  --ufio-radius-full: 9999px;

  /* === QUANTUM TRANSITIONS === */
  --ufio-transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --ufio-transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  --ufio-transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
  --ufio-transition-bounce: 600ms cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --ufio-transition-quantum: 800ms cubic-bezier(0.23, 1, 0.32, 1);

  /* === Z-INDEX LAYERS === */
  --ufio-z-dropdown: 1000;
  --ufio-z-sticky: 1020;
  --ufio-z-fixed: 1030;
  --ufio-z-modal-backdrop: 1040;
  --ufio-z-modal: 1050;
  --ufio-z-popover: 1060;
  --ufio-z-tooltip: 1070;
  --ufio-z-toast: 1080;
  --ufio-z-quantum: 9999;
}

/* ===== ENTERPRISE BASE STYLES ===== */
* {
  box-sizing: border-box;
}

.ufio-enterprise-wrapper {
  font-family: var(--ufio-font-family-primary);
  font-size: var(--ufio-text-base);
  line-height: 1.6;
  color: var(--ufio-gray-800);
  background: var(--ufio-gray-50);
  min-height: 100vh;
}

/* ===== QUANTUM LAYOUT SYSTEM ===== */
.ufio-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: var(--ufio-space-6);
}

.ufio-container-fluid {
  width: 100%;
  padding: var(--ufio-space-6);
}

/* Quantum Grid System */
.ufio-grid {
  display: grid;
  gap: var(--ufio-space-6);
}

.ufio-grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.ufio-grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.ufio-grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.ufio-grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.ufio-grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
.ufio-grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }

.ufio-grid-auto-fit-sm { grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); }
.ufio-grid-auto-fit-md { grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); }
.ufio-grid-auto-fit-lg { grid-template-columns: repeat(auto-fit, minmax(450px, 1fr)); }

/* Quantum Flexbox System */
.ufio-flex {
  display: flex;
}

.ufio-flex-col {
  flex-direction: column;
}

.ufio-flex-wrap {
  flex-wrap: wrap;
}

.ufio-items-center {
  align-items: center;
}

.ufio-items-start {
  align-items: flex-start;
}

.ufio-items-end {
  align-items: flex-end;
}

.ufio-justify-center {
  justify-content: center;
}

.ufio-justify-between {
  justify-content: space-between;
}

.ufio-justify-around {
  justify-content: space-around;
}

.ufio-justify-evenly {
  justify-content: space-evenly;
}

/* ===== ENTERPRISE CARD SYSTEM ===== */
.ufio-card {
  background: white;
  border-radius: var(--ufio-radius-xl);
  box-shadow: var(--ufio-shadow-base);
  padding: var(--ufio-space-6);
  transition: var(--ufio-transition-normal);
  border: 1px solid var(--ufio-gray-200);
}

.ufio-card:hover {
  box-shadow: var(--ufio-shadow-lg);
  transform: translateY(-2px);
}

.ufio-card-premium {
  background: var(--ufio-gradient-premium);
  color: white;
  border: none;
  box-shadow: var(--ufio-shadow-quantum);
}

.ufio-card-enterprise {
  background: var(--ufio-gradient-enterprise);
  border: 2px solid var(--ufio-primary-200);
  box-shadow: var(--ufio-shadow-enterprise);
}

.ufio-card-quantum {
  background: var(--ufio-gradient-quantum);
  border: none;
  box-shadow: var(--ufio-shadow-2xl);
  position: relative;
  overflow: hidden;
}

.ufio-card-quantum::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s;
}

.ufio-card-quantum:hover::before {
  transform: translateX(100%);
}

/* Card Headers */
.ufio-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--ufio-space-6);
  padding-bottom: var(--ufio-space-4);
  border-bottom: 1px solid var(--ufio-gray-200);
}

.ufio-card-title {
  font-size: var(--ufio-text-xl);
  font-weight: var(--ufio-font-weight-bold);
  color: var(--ufio-gray-900);
  margin: 0;
}

.ufio-card-actions {
  display: flex;
  gap: var(--ufio-space-2);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .ufio-container {
    padding: var(--ufio-space-4);
  }
  
  .ufio-grid {
    gap: var(--ufio-space-4);
  }
  
  .ufio-grid-cols-2,
  .ufio-grid-cols-3,
  .ufio-grid-cols-4,
  .ufio-grid-cols-5,
  .ufio-grid-cols-6 {
    grid-template-columns: 1fr;
  }
  
  .ufio-card {
    padding: var(--ufio-space-4);
  }
}

@media (max-width: 480px) {
  .ufio-container {
    padding: var(--ufio-space-3);
  }
  
  .ufio-card {
    padding: var(--ufio-space-3);
  }
}
