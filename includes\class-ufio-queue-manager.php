<?php
/**
 * UFIO Queue Manager
 * Handles priority-based processing queue with traffic and SEO weighting
 */

if (!defined('ABSPATH')) {
    exit;
}

class UFIO_Queue_Manager {
    
    private static $instance = null;
    private $worker_id;
    private $max_processing_time = 300; // 5 minutes
    private $lock_timeout = 600; // 10 minutes
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        $this->worker_id = uniqid('worker_', true);
        
        // Schedule priority calculation
        add_action('ufio_calculate_priorities', [$this, 'calculate_all_priorities']);
        add_action('ufio_process_queue', [$this, 'process_queue']);
        add_action('ufio_cleanup_queue', [$this, 'cleanup_queue']);
        
        // Schedule daily priority recalculation
        if (!wp_next_scheduled('ufio_calculate_priorities')) {
            wp_schedule_event(time(), 'daily', 'ufio_calculate_priorities');
        }
        
        // Schedule queue processing every 5 minutes
        if (!wp_next_scheduled('ufio_process_queue')) {
            wp_schedule_event(time(), 'ufio_5min', 'ufio_process_queue');
        }
    }
    
    /**
     * Add post to processing queue
     */
    public function add_to_queue($post_id, $priority_override = null) {
        global $wpdb;
        
        $post = get_post($post_id);
        if (!$post || $post->post_status !== 'publish') {
            return false;
        }
        
        // Skip if already has featured image
        if (get_post_thumbnail_id($post_id)) {
            return false;
        }
        
        $queue_table = $wpdb->prefix . 'ufio_queue';
        
        // Check if already in queue
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $queue_table WHERE post_id = %d",
            $post_id
        ));
        
        if ($existing) {
            return $existing;
        }
        
        // Calculate priority score
        $priority_score = $priority_override ?? $this->calculate_priority_score($post_id);
        $traffic_weight = $this->get_traffic_weight($post_id);
        $seo_potential = $this->get_seo_potential($post_id);
        
        $result = $wpdb->insert($queue_table, [
            'post_id' => $post_id,
            'priority_score' => $priority_score,
            'traffic_weight' => $traffic_weight,
            'seo_potential' => $seo_potential,
            'status' => 'pending'
        ]);
        
        return $result ? $wpdb->insert_id : false;
    }
    
    /**
     * Calculate priority score for a post
     */
    public function calculate_priority_score($post_id) {
        $traffic_weight = $this->get_traffic_weight($post_id);
        $seo_potential = $this->get_seo_potential($post_id);
        
        // Weighted formula: 60% traffic, 40% SEO potential
        $priority_score = ($traffic_weight * 0.6) + ($seo_potential * 0.4);
        
        return round($priority_score, 4);
    }
    
    /**
     * Get traffic weight from analytics
     */
    private function get_traffic_weight($post_id) {
        // Try to get from Google Analytics 4 or Search Console
        $ga4_data = $this->get_ga4_traffic_data($post_id);
        if ($ga4_data !== false) {
            return $ga4_data;
        }
        
        // Fallback to WordPress stats
        $post = get_post($post_id);
        $days_old = (time() - strtotime($post->post_date)) / DAY_IN_SECONDS;
        
        // Newer posts get higher weight
        $recency_weight = max(0, 100 - ($days_old * 2));
        
        // Posts with more comments get higher weight
        $comment_weight = min(50, $post->comment_count * 5);
        
        // Combine factors
        $traffic_weight = ($recency_weight * 0.7) + ($comment_weight * 0.3);
        
        return round(min(100, max(0, $traffic_weight)), 4);
    }
    
    /**
     * Get SEO potential score
     */
    private function get_seo_potential($post_id) {
        $post = get_post($post_id);
        $score = 0;
        
        // Content length factor (longer content = higher potential)
        $content_length = str_word_count(strip_tags($post->post_content));
        $length_score = min(30, $content_length / 50); // Max 30 points
        
        // Title optimization factor
        $title_length = strlen($post->post_title);
        $title_score = ($title_length >= 30 && $title_length <= 60) ? 20 : 10;
        
        // Category/tag factor
        $categories = wp_get_post_categories($post_id);
        $tags = wp_get_post_tags($post_id);
        $taxonomy_score = min(20, (count($categories) * 5) + (count($tags) * 2));
        
        // Meta description factor
        $meta_desc = get_post_meta($post_id, '_yoast_wpseo_metadesc', true) ?: 
                    get_post_meta($post_id, '_aioseop_description', true);
        $meta_score = $meta_desc ? 15 : 0;
        
        // Internal links factor
        $internal_links = substr_count($post->post_content, home_url());
        $link_score = min(15, $internal_links * 3);
        
        $score = $length_score + $title_score + $taxonomy_score + $meta_score + $link_score;
        
        return round(min(100, max(0, $score)), 4);
    }
    
    /**
     * Get GA4 traffic data (placeholder for real implementation)
     */
    private function get_ga4_traffic_data($post_id) {
        // This would integrate with Google Analytics 4 API
        // For now, return false to use fallback
        return false;
    }
    
    /**
     * Process queue items
     */
    public function process_queue($max_items = 10) {
        global $wpdb;
        
        $start_time = microtime(true);
        $queue_table = $wpdb->prefix . 'ufio_queue';
        
        // Get highest priority pending items
        $items = $wpdb->get_results($wpdb->prepare("
            SELECT * FROM $queue_table 
            WHERE status = 'pending' 
            AND (locked_until IS NULL OR locked_until < NOW())
            AND attempts < max_attempts
            ORDER BY priority_score DESC, created_at ASC
            LIMIT %d
        ", $max_items), ARRAY_A);
        
        if (empty($items)) {
            return 0;
        }
        
        $processed = 0;
        
        foreach ($items as $item) {
            // Check processing time limit
            if ((microtime(true) - $start_time) > $this->max_processing_time) {
                break;
            }
            
            if ($this->process_queue_item($item)) {
                $processed++;
            }
        }
        
        return $processed;
    }
    
    /**
     * Process individual queue item
     */
    private function process_queue_item($item) {
        global $wpdb;
        
        $queue_table = $wpdb->prefix . 'ufio_queue';
        $post_id = $item['post_id'];
        $queue_id = $item['id'];
        
        // Lock the item
        $lock_until = date('Y-m-d H:i:s', time() + $this->lock_timeout);
        $locked = $wpdb->update($queue_table, [
            'status' => 'processing',
            'locked_until' => $lock_until,
            'locked_by' => $this->worker_id,
            'attempts' => $item['attempts'] + 1,
            'updated_at' => current_time('mysql')
        ], ['id' => $queue_id]);
        
        if (!$locked) {
            return false;
        }
        
        try {
            $start_time = microtime(true);
            
            // Process the post
            $result = $this->process_post($post_id);
            
            $processing_time = (microtime(true) - $start_time) * 1000;
            
            if ($result) {
                // Mark as completed
                $wpdb->update($queue_table, [
                    'status' => 'completed',
                    'completed_at' => current_time('mysql'),
                    'locked_until' => null,
                    'locked_by' => null,
                    'error_message' => null
                ], ['id' => $queue_id]);
                
                // Log success
                $this->log_analytics('single_process', $post_id, null, [
                    'processing_time_ms' => $processing_time,
                    'success' => true,
                    'queue_id' => $queue_id
                ]);
                
                return true;
            } else {
                throw new Exception('Post processing returned false');
            }
            
        } catch (Exception $e) {
            // Handle failure
            $this->handle_processing_failure($queue_id, $item, $e->getMessage());
            return false;
        }
    }
    
    /**
     * Process individual post
     */
    private function process_post($post_id) {
        // Get or queue AI analysis
        $analysis = UFIO_Batch_Processor::get_instance()->get_cached_analysis($post_id);
        
        if (!$analysis) {
            // Queue for batch processing
            UFIO_Batch_Processor::get_instance()->queue_post_analysis($post_id, 8);
            return false; // Will be processed in next batch
        }
        
        // Use microservice for image search and scoring
        $microservice_result = UFIO_Microservice_Client::get_instance()->queue_image_search($post_id, $analysis);
        
        if (!$microservice_result) {
            // Fallback to local processing
            return $this->process_post_locally($post_id, $analysis);
        }
        
        return true;
    }
    
    /**
     * Fallback local processing
     */
    private function process_post_locally($post_id, $analysis) {
        $post = get_post($post_id);
        if (!$post) {
            return false;
        }
        
        // Simple local image search
        $keywords = $analysis['keywords'] ?? [];
        if (empty($keywords)) {
            return false;
        }
        
        // Search for images
        $args = [
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'post_status' => 'inherit',
            'posts_per_page' => 5,
            's' => implode(' ', array_slice($keywords, 0, 3))
        ];
        
        $images = get_posts($args);
        
        if (!empty($images)) {
            $best_image = $images[0];
            set_post_thumbnail($post_id, $best_image->ID);
            
            // Optimize image SEO
            $this->optimize_image_seo($best_image->ID, $post, $analysis);
            
            return true;
        }
        
        return false;
    }
    
    /**
     * Optimize image SEO
     */
    private function optimize_image_seo($image_id, $post, $analysis) {
        $keywords = $analysis['keywords'] ?? [];
        if (empty($keywords)) {
            return;
        }
        
        // Generate optimized alt text
        $alt_text = sprintf('%s related to %s', $keywords[0], $post->post_title);
        update_post_meta($image_id, '_wp_attachment_image_alt', $alt_text);
        
        // Update image title if needed
        $image = get_post($image_id);
        if ($image && (empty($image->post_title) || strpos($image->post_title, 'IMG_') !== false)) {
            wp_update_post([
                'ID' => $image_id,
                'post_title' => sprintf('%s - %s', $keywords[0], $post->post_title)
            ]);
        }
    }
    
    /**
     * Handle processing failure
     */
    private function handle_processing_failure($queue_id, $item, $error_message) {
        global $wpdb;
        
        $queue_table = $wpdb->prefix . 'ufio_queue';
        
        // Calculate backoff delay
        $backoff_delay = min(3600, pow(2, $item['attempts']) * 60); // Exponential backoff, max 1 hour
        $retry_after = date('Y-m-d H:i:s', time() + $backoff_delay);
        
        if ($item['attempts'] >= $item['max_attempts']) {
            // Mark as failed
            $wpdb->update($queue_table, [
                'status' => 'failed',
                'error_message' => $error_message,
                'locked_until' => null,
                'locked_by' => null
            ], ['id' => $queue_id]);
        } else {
            // Schedule retry
            $wpdb->update($queue_table, [
                'status' => 'pending',
                'error_message' => $error_message,
                'locked_until' => $retry_after,
                'locked_by' => null
            ], ['id' => $queue_id]);
        }
        
        // Log error
        $this->log_analytics('error', $item['post_id'], null, [
            'error' => $error_message,
            'attempts' => $item['attempts'],
            'queue_id' => $queue_id
        ]);
    }
    
    /**
     * Calculate priorities for all posts
     */
    public function calculate_all_priorities() {
        global $wpdb;
        
        $queue_table = $wpdb->prefix . 'ufio_queue';
        
        // Get all pending and failed items
        $items = $wpdb->get_results("
            SELECT id, post_id FROM $queue_table 
            WHERE status IN ('pending', 'failed')
        ", ARRAY_A);
        
        foreach ($items as $item) {
            $priority_score = $this->calculate_priority_score($item['post_id']);
            $traffic_weight = $this->get_traffic_weight($item['post_id']);
            $seo_potential = $this->get_seo_potential($item['post_id']);
            
            $wpdb->update($queue_table, [
                'priority_score' => $priority_score,
                'traffic_weight' => $traffic_weight,
                'seo_potential' => $seo_potential
            ], ['id' => $item['id']]);
        }
    }
    
    /**
     * Get top priority posts for dashboard
     */
    public function get_top_priority_posts($limit = 100) {
        global $wpdb;
        
        $queue_table = $wpdb->prefix . 'ufio_queue';
        
        return $wpdb->get_results($wpdb->prepare("
            SELECT q.*, p.post_title, p.post_date
            FROM $queue_table q
            JOIN {$wpdb->posts} p ON q.post_id = p.ID
            WHERE q.status = 'pending'
            ORDER BY q.priority_score DESC, q.created_at ASC
            LIMIT %d
        ", $limit), ARRAY_A);
    }
    
    /**
     * Cleanup old queue items
     */
    public function cleanup_queue() {
        UFIO_Database::get_instance()->cleanup_old_data();
    }
    
    /**
     * Log analytics
     */
    private function log_analytics($event_type, $post_id = null, $batch_id = null, $metadata = []) {
        global $wpdb;
        
        $analytics_table = $wpdb->prefix . 'ufio_analytics';
        
        $wpdb->insert($analytics_table, [
            'event_type' => $event_type,
            'post_id' => $post_id,
            'batch_id' => $batch_id,
            'processing_time_ms' => $metadata['processing_time_ms'] ?? null,
            'memory_usage_mb' => round(memory_get_usage(true) / 1024 / 1024, 2),
            'success' => $metadata['success'] ?? false,
            'error_code' => $metadata['error'] ?? null,
            'metadata' => json_encode($metadata)
        ]);
    }
    
    /**
     * Get queue statistics
     */
    public function get_queue_statistics() {
        return UFIO_Database::get_instance()->get_queue_stats();
    }
}
