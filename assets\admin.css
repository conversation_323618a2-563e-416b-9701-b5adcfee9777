/**
 * Ultra Featured Image Optimizer Pro - Professional Admin Styles
 * Modern, responsive, and beautiful interface
 */

:root {
    --ufio-primary: #2271b1;
    --ufio-primary-dark: #135e96;
    --ufio-success: #00a32a;
    --ufio-warning: #dba617;
    --ufio-error: #d63638;
    --ufio-info: #72aee6;
    --ufio-gray-50: #f9fafb;
    --ufio-gray-100: #f3f4f6;
    --ufio-gray-200: #e5e7eb;
    --ufio-gray-300: #d1d5db;
    --ufio-gray-400: #9ca3af;
    --ufio-gray-500: #6b7280;
    --ufio-gray-600: #4b5563;
    --ufio-gray-700: #374151;
    --ufio-gray-800: #1f2937;
    --ufio-gray-900: #111827;
    --ufio-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --ufio-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --ufio-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --ufio-border-radius: 8px;
    --ufio-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Base Styles */
.ufio-dashboard,
.ufio-bulk-page,
.ufio-settings-page,
.ufio-analytics-page {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.ufio-dashboard h1,
.ufio-bulk-page h1,
.ufio-settings-page h1,
.ufio-analytics-page h1 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--ufio-gray-900);
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

/* Grid Layouts */
.ufio-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.ufio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.ufio-analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

/* Cards */
.ufio-card {
    background: #fff;
    border-radius: var(--ufio-border-radius);
    box-shadow: var(--ufio-shadow);
    padding: 1.5rem;
    border: 1px solid var(--ufio-gray-200);
    transition: var(--ufio-transition);
}

.ufio-card:hover {
    box-shadow: var(--ufio-shadow-lg);
    transform: translateY(-2px);
}

.ufio-card h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--ufio-gray-900);
    margin: 0 0 1rem 0;
    padding-bottom: 0.75rem;
    border-bottom: 2px solid var(--ufio-gray-100);
}

.ufio-card h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--ufio-gray-800);
    margin: 1.5rem 0 0.75rem 0;
}

/* Stat Cards */
.ufio-stat-card {
    background: linear-gradient(135deg, var(--ufio-primary), var(--ufio-primary-dark));
    color: #fff;
    padding: 1.5rem;
    border-radius: var(--ufio-border-radius);
    text-align: center;
    position: relative;
    overflow: hidden;
    transition: var(--ufio-transition);
}

.ufio-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), transparent);
    pointer-events: none;
}

.ufio-stat-card:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: var(--ufio-shadow-lg);
}

.ufio-stat-card.success {
    background: linear-gradient(135deg, var(--ufio-success), #007a1f);
}

.ufio-stat-card.warning {
    background: linear-gradient(135deg, var(--ufio-warning), #b8860b);
}

.ufio-stat-card.info {
    background: linear-gradient(135deg, var(--ufio-info), #3b82f6);
}

.ufio-stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1;
    margin-bottom: 0.5rem;
    display: block;
}

.ufio-stat-label {
    font-size: 0.875rem;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-weight: 500;
}

/* Buttons */
.ufio-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--ufio-border-radius);
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--ufio-transition);
    position: relative;
    overflow: hidden;
    white-space: nowrap;
}

.ufio-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--ufio-shadow-lg);
}

.ufio-btn:active {
    transform: translateY(0);
}

.ufio-btn.primary {
    background: var(--ufio-primary);
    color: #fff;
}

.ufio-btn.primary:hover {
    background: var(--ufio-primary-dark);
}

.ufio-btn.secondary {
    background: var(--ufio-gray-100);
    color: var(--ufio-gray-700);
    border: 1px solid var(--ufio-gray-300);
}

.ufio-btn.secondary:hover {
    background: var(--ufio-gray-200);
    border-color: var(--ufio-gray-400);
}

.ufio-btn.success {
    background: var(--ufio-success);
    color: #fff;
}

.ufio-btn.warning {
    background: var(--ufio-warning);
    color: #fff;
}

.ufio-btn.info {
    background: var(--ufio-info);
    color: #fff;
}

.ufio-btn.large {
    padding: 1rem 2rem;
    font-size: 1rem;
}

.ufio-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

/* Action Buttons */
.ufio-action-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Status Indicators */
.ufio-status-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.ufio-status-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: var(--ufio-gray-50);
    border-radius: var(--ufio-border-radius);
    border: 1px solid var(--ufio-gray-200);
}

.ufio-status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    flex-shrink: 0;
}

.ufio-status-indicator.success {
    background: var(--ufio-success);
    box-shadow: 0 0 0 2px rgba(0, 163, 42, 0.2);
}

.ufio-status-indicator.warning {
    background: var(--ufio-warning);
    box-shadow: 0 0 0 2px rgba(219, 166, 23, 0.2);
}

.ufio-status-indicator.error {
    background: var(--ufio-error);
    box-shadow: 0 0 0 2px rgba(214, 54, 56, 0.2);
}

.ufio-status-label {
    font-weight: 500;
    color: var(--ufio-gray-700);
}

/* Progress Bars */
.ufio-progress-section {
    margin: 1.5rem 0;
}

.ufio-progress-item {
    margin-bottom: 1rem;
}

.ufio-progress-item label {
    display: block;
    font-weight: 500;
    color: var(--ufio-gray-700);
    margin-bottom: 0.5rem;
}

.ufio-progress-bar {
    position: relative;
    background: var(--ufio-gray-200);
    border-radius: 9999px;
    height: 1.5rem;
    overflow: hidden;
}

.ufio-progress-fill {
    background: linear-gradient(90deg, var(--ufio-primary), var(--ufio-info));
    height: 100%;
    border-radius: 9999px;
    transition: width 0.5s ease;
    position: relative;
}

.ufio-progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.ufio-progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--ufio-gray-700);
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

/* Processing History */
.ufio-processing-history {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--ufio-gray-200);
}

.ufio-last-run {
    font-size: 0.875rem;
    color: var(--ufio-gray-600);
    margin: 0.5rem 0 0 0;
}

/* Bulk Processing */
.ufio-bulk-controls {
    display: flex;
    gap: 1rem;
    align-items: end;
    flex-wrap: wrap;
    margin-bottom: 2rem;
}

.ufio-form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.ufio-form-group label {
    font-weight: 500;
    color: var(--ufio-gray-700);
    font-size: 0.875rem;
}

.ufio-form-group select,
.ufio-form-group input {
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--ufio-gray-300);
    border-radius: var(--ufio-border-radius);
    font-size: 0.875rem;
    transition: var(--ufio-transition);
}

.ufio-form-group select:focus,
.ufio-form-group input:focus {
    outline: none;
    border-color: var(--ufio-primary);
    box-shadow: 0 0 0 3px rgba(34, 113, 177, 0.1);
}

/* Progress Container */
.ufio-progress-container {
    margin: 2rem 0;
    padding: 1.5rem;
    background: var(--ufio-gray-50);
    border-radius: var(--ufio-border-radius);
    border: 1px solid var(--ufio-gray-200);
}

.ufio-progress-details {
    margin-top: 1rem;
    text-align: center;
}

#ufio-progress-status {
    font-weight: 500;
    color: var(--ufio-gray-700);
}

/* Results Container */
.ufio-results-container {
    margin: 2rem 0;
    padding: 1.5rem;
    background: var(--ufio-success);
    color: #fff;
    border-radius: var(--ufio-border-radius);
}

.ufio-results-container h3 {
    margin: 0 0 1rem 0;
    color: #fff;
}

/* Single Processing */
.ufio-single-controls {
    display: flex;
    gap: 1rem;
    align-items: end;
    flex-wrap: wrap;
}

.ufio-single-result {
    margin-top: 1rem;
    padding: 1rem;
    border-radius: var(--ufio-border-radius);
}

.ufio-single-result.success {
    background: rgba(0, 163, 42, 0.1);
    border: 1px solid var(--ufio-success);
    color: var(--ufio-success);
}

.ufio-single-result.error {
    background: rgba(214, 54, 56, 0.1);
    border: 1px solid var(--ufio-error);
    color: var(--ufio-error);
}

/* Analytics */
.ufio-analytics-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
}

.ufio-analytics-item {
    text-align: center;
    padding: 1rem;
    background: var(--ufio-gray-50);
    border-radius: var(--ufio-border-radius);
    border: 1px solid var(--ufio-gray-200);
}

.ufio-analytics-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--ufio-primary);
    margin-bottom: 0.25rem;
}

.ufio-analytics-label {
    font-size: 0.75rem;
    color: var(--ufio-gray-600);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Performance Metrics */
.ufio-performance-metrics {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.ufio-metric {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: var(--ufio-gray-50);
    border-radius: var(--ufio-border-radius);
    border: 1px solid var(--ufio-gray-200);
}

.ufio-metric-label {
    font-weight: 500;
    color: var(--ufio-gray-700);
}

.ufio-metric-value {
    font-weight: 600;
    color: var(--ufio-gray-900);
}

/* Cache Controls */
.ufio-cache-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.ufio-cache-result {
    padding: 0.75rem 1rem;
    border-radius: var(--ufio-border-radius);
    font-weight: 500;
}

.ufio-cache-result.success {
    background: rgba(0, 163, 42, 0.1);
    color: var(--ufio-success);
    border: 1px solid var(--ufio-success);
}

/* Range Input */
.ufio-range {
    width: 200px;
    margin-right: 1rem;
}

.ufio-range-value {
    font-weight: 600;
    color: var(--ufio-primary);
    min-width: 40px;
    display: inline-block;
}

/* Loading States */
.ufio-loading {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--ufio-gray-600);
}

.ufio-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid var(--ufio-gray-300);
    border-top: 2px solid var(--ufio-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .ufio-dashboard,
    .ufio-bulk-page,
    .ufio-settings-page,
    .ufio-analytics-page {
        padding: 1rem;
    }
    
    .ufio-stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;
    }
    
    .ufio-grid,
    .ufio-analytics-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .ufio-action-buttons,
    .ufio-bulk-controls,
    .ufio-single-controls,
    .ufio-cache-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .ufio-btn {
        justify-content: center;
    }
    
    .ufio-analytics-stats {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .ufio-stats-grid {
        grid-template-columns: 1fr;
    }
    
    .ufio-analytics-stats {
        grid-template-columns: 1fr;
    }
    
    .ufio-metric {
        flex-direction: column;
        gap: 0.25rem;
        text-align: center;
    }
}

/* Image Management Section Styles */
.image-management-section {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: var(--ufio-border-radius);
    padding: 1.5rem;
    margin-top: 1.5rem;
    border: 1px solid var(--ufio-gray-200);
}

.image-management-section h5 {
    color: var(--ufio-gray-700);
    font-weight: 600;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.management-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.featured-image-group,
.cleanup-group {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

/* Enhanced button styles for new actions */
.btn.btn-warning {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    border: none;
    transition: var(--ufio-transition);
}

.btn.btn-warning:hover {
    background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.btn.btn-info {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    border: none;
    transition: var(--ufio-transition);
}

.btn.btn-info:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.btn.btn-danger {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    color: white;
    border: none;
    transition: var(--ufio-transition);
}

.btn.btn-danger:hover {
    background: linear-gradient(135deg, #b91c1c 0%, #991b1b 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
}

.current-status {
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid var(--ufio-gray-200);
    border-radius: 6px;
    padding: 0.75rem;
    font-size: 0.875rem;
    color: var(--ufio-gray-600);
    backdrop-filter: blur(10px);
}

.current-status strong {
    color: var(--ufio-gray-800);
}

/* Responsive adjustments for image management */
@media (max-width: 768px) {
    .management-buttons {
        flex-direction: column;
        align-items: stretch;
    }

    .featured-image-group,
    .cleanup-group {
        flex-direction: column;
        width: 100%;
    }

    .featured-image-group .btn,
    .cleanup-group .btn {
        width: 100%;
        justify-content: center;
    }
}
