<?php
/**
 * UFIO Enhanced Dashboard
 * Beautiful, real-time dashboard with advanced analytics and controls
 */

if (!defined('ABSPATH')) {
    exit;
}

class UFIO_Dashboard {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        add_action('admin_menu', [$this, 'add_dashboard_pages']);
        add_action('admin_enqueue_scripts', [$this, 'enqueue_dashboard_assets']);
        
        // AJAX handlers for real-time updates
        add_action('wp_ajax_ufio_dashboard_stats', [$this, 'ajax_dashboard_stats']);
        add_action('wp_ajax_ufio_queue_stats', [$this, 'ajax_queue_stats']);
        add_action('wp_ajax_ufio_performance_metrics', [$this, 'ajax_performance_metrics']);
        add_action('wp_ajax_ufio_top_priority_posts', [$this, 'ajax_top_priority_posts']);
        add_action('wp_ajax_ufio_microservice_status', [$this, 'ajax_microservice_status']);
        add_action('wp_ajax_ufio_get_post_urls', [$this, 'ajax_get_post_urls']);
        add_action('wp_ajax_ufio_insert_content_image', [$this, 'ajax_insert_content_image']);
    }
    
    /**
     * Add dashboard pages
     */
    public function add_dashboard_pages() {
        // Command Center - Single Page App
        add_menu_page(
            __('UFIO Command Center', 'ultra-featured-image-optimizer'),
            __('UFIO Pro', 'ultra-featured-image-optimizer'),
            'manage_options',
            'ufio-command-center',
            [$this, 'render_command_center'],
            'dashicons-format-image',
            30
        );
        
        // Performance Analytics
        add_submenu_page(
            'ufio-pro-dashboard',
            __('Performance Analytics', 'ultra-featured-image-optimizer'),
            __('Analytics', 'ultra-featured-image-optimizer'),
            'manage_options',
            'ufio-analytics',
            [$this, 'render_analytics_page']
        );
        
        // Queue Management
        add_submenu_page(
            'ufio-pro-dashboard',
            __('Queue Management', 'ultra-featured-image-optimizer'),
            __('Queue', 'ultra-featured-image-optimizer'),
            'manage_options',
            'ufio-queue',
            [$this, 'render_queue_page']
        );
        
        // System Status
        add_submenu_page(
            'ufio-pro-dashboard',
            __('System Status', 'ultra-featured-image-optimizer'),
            __('System', 'ultra-featured-image-optimizer'),
            'manage_options',
            'ufio-system',
            [$this, 'render_system_page']
        );
        
        // Post URLs & Image Insertion
        add_submenu_page(
            'ufio-pro-dashboard',
            __('Post URLs & Images', 'ultra-featured-image-optimizer'),
            __('Post URLs', 'ultra-featured-image-optimizer'),
            'manage_options',
            'ufio-post-urls',
            [$this, 'render_post_urls_page']
        );

        // Settings
        add_submenu_page(
            'ufio-pro-dashboard',
            __('Settings', 'ultra-featured-image-optimizer'),
            __('Settings', 'ultra-featured-image-optimizer'),
            'manage_options',
            'ufio-settings',
            [$this, 'render_settings_page']
        );
    }
    
    /**
     * Enqueue dashboard assets
     */
    public function enqueue_dashboard_assets($hook) {
        if (strpos($hook, 'ufio-') === false) {
            return;
        }
        
        // Enhanced CSS
        wp_enqueue_style(
            'ufio-dashboard',
            UFIO_PLUGIN_URL . 'assets/dashboard.css',
            [],
            UFIO_VERSION
        );
        
        // Chart.js for analytics
        wp_enqueue_script(
            'chartjs',
            'https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js',
            [],
            '3.9.1',
            true
        );
        
        // Enhanced JavaScript
        wp_enqueue_script(
            'ufio-dashboard',
            UFIO_PLUGIN_URL . 'assets/dashboard.js',
            ['jquery', 'chartjs'],
            UFIO_VERSION,
            true
        );
        
        // Localize script
        wp_localize_script('ufio-dashboard', 'ufioAjax', [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('ufio_dashboard_nonce'),
            'refreshInterval' => 30000, // 30 seconds
            'strings' => [
                'loading' => __('Loading...', 'ultra-featured-image-optimizer'),
                'error' => __('Error loading data', 'ultra-featured-image-optimizer'),
                'noData' => __('No data available', 'ultra-featured-image-optimizer'),
                'updated' => __('Data updated', 'ultra-featured-image-optimizer')
            ]
        ]);
    }
    
    /**
     * Render Command Center SPA
     */
    public function render_command_center() {
        // Get current user capabilities
        $user_capabilities = [
            'manage_options' => current_user_can('manage_options'),
            'edit_posts' => current_user_can('edit_posts'),
            'upload_files' => current_user_can('upload_files')
        ];

        // Get plugin settings
        $settings = get_option('ufio_options', []);

        // WebSocket URL (if available)
        $ws_url = defined('UFIO_WEBSOCKET_URL') ? UFIO_WEBSOCKET_URL : 'ws://localhost:8080';

        ?>
        <!DOCTYPE html>
        <html <?php language_attributes(); ?>>
        <head>
            <meta charset="<?php bloginfo('charset'); ?>">
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <meta name="theme-color" content="#7c6df2">
            <title><?php _e('UFIO Command Center', 'ultra-featured-image-optimizer'); ?></title>

            <!-- Preload critical resources -->
            <link rel="preload" href="<?php echo UFIO_PLUGIN_URL; ?>command-center/dist/static/js/main.js" as="script">
            <link rel="preload" href="<?php echo UFIO_PLUGIN_URL; ?>command-center/dist/static/css/main.css" as="style">

            <!-- Critical CSS inline for instant loading -->
            <style>
                body { margin: 0; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; }
                #ufio-command-center { min-height: 100vh; background: #f9fafb; }
                .loading-screen { display: flex; align-items: center; justify-content: center; min-height: 100vh; flex-direction: column; gap: 1rem; }
                .loading-spinner { width: 40px; height: 40px; border: 3px solid #e5e7eb; border-top: 3px solid #7c6df2; border-radius: 50%; animation: spin 1s linear infinite; }
                @keyframes spin { to { transform: rotate(360deg); } }
            </style>

            <?php wp_head(); ?>
        </head>
        <body class="ufio-command-center-body">
            <!-- Skip to content link for accessibility -->
            <a href="#main-content" class="skip-link">Skip to main content</a>

            <!-- Command Center App Container -->
            <div id="ufio-command-center">
                <div class="loading-screen">
                    <div class="loading-spinner"></div>
                    <p>Loading Command Center...</p>
                </div>
            </div>

            <!-- WordPress & Plugin Configuration -->
            <script>
                window.wpApiSettings = {
                    root: '<?php echo esc_url_raw(rest_url()); ?>',
                    nonce: '<?php echo wp_create_nonce('wp_rest'); ?>'
                };

                window.ufioSettings = {
                    pluginUrl: '<?php echo UFIO_PLUGIN_URL; ?>',
                    wsUrl: '<?php echo esc_url($ws_url); ?>',
                    currentUser: {
                        id: <?php echo get_current_user_id(); ?>,
                        name: '<?php echo esc_js(wp_get_current_user()->display_name); ?>',
                        email: '<?php echo esc_js(wp_get_current_user()->user_email); ?>'
                    },
                    capabilities: <?php echo json_encode($user_capabilities); ?>,
                    settings: <?php echo json_encode($settings); ?>,
                    version: '<?php echo UFIO_VERSION; ?>',
                    apiUrl: '<?php echo esc_url_raw(rest_url('ufio/v1/')); ?>'
                };
            </script>

            <!-- Load React App -->
            <script src="<?php echo UFIO_PLUGIN_URL; ?>command-center/dist/static/js/main.js" defer></script>
            <link rel="stylesheet" href="<?php echo UFIO_PLUGIN_URL; ?>command-center/dist/static/css/main.css">

            <?php wp_footer(); ?>
        </body>
        </html>
        <?php
        exit; // Prevent WordPress from loading admin footer
    }

    /**
     * Render main dashboard (legacy fallback)
     */
    public function render_main_dashboard() {
        $stats = $this->get_dashboard_overview();
        ?>
        <div class="wrap ufio-dashboard-wrap">
            <div class="ufio-dashboard-header">
                <h1>
                    <span class="ufio-logo">🚀</span>
                    <?php _e('Ultra Featured Image Optimizer Pro', 'ultra-featured-image-optimizer'); ?>
                    <span class="ufio-version">v<?php echo UFIO_VERSION; ?></span>
                </h1>
                <div class="ufio-header-actions">
                    <button class="ufio-btn primary" id="ufio-refresh-all">
                        <span class="dashicons dashicons-update"></span>
                        <?php _e('Refresh All', 'ultra-featured-image-optimizer'); ?>
                    </button>
                    <div class="ufio-status-indicator" id="ufio-system-status">
                        <span class="ufio-status-dot success"></span>
                        <span><?php _e('System Healthy', 'ultra-featured-image-optimizer'); ?></span>
                    </div>
                </div>
            </div>

            <!-- Key Metrics Grid -->
            <div class="ufio-metrics-grid">
                <div class="ufio-metric-card primary" data-metric="total-posts">
                    <div class="ufio-metric-icon">📝</div>
                    <div class="ufio-metric-content">
                        <div class="ufio-metric-number" id="total-posts"><?php echo number_format($stats['total_posts']); ?></div>
                        <div class="ufio-metric-label"><?php _e('Total Posts', 'ultra-featured-image-optimizer'); ?></div>
                        <div class="ufio-metric-change positive">+<?php echo $stats['posts_this_week']; ?> this week</div>
                    </div>
                </div>

                <div class="ufio-metric-card success" data-metric="coverage">
                    <div class="ufio-metric-icon">🎯</div>
                    <div class="ufio-metric-content">
                        <div class="ufio-metric-number" id="coverage-rate"><?php echo $stats['coverage_percentage']; ?>%</div>
                        <div class="ufio-metric-label"><?php _e('Coverage Rate', 'ultra-featured-image-optimizer'); ?></div>
                        <div class="ufio-metric-change positive">+<?php echo $stats['coverage_improvement']; ?>% this month</div>
                    </div>
                </div>

                <div class="ufio-metric-card warning" data-metric="queue">
                    <div class="ufio-metric-icon">⏳</div>
                    <div class="ufio-metric-content">
                        <div class="ufio-metric-number" id="queue-size"><?php echo number_format($stats['queue_pending']); ?></div>
                        <div class="ufio-metric-label"><?php _e('Queue Pending', 'ultra-featured-image-optimizer'); ?></div>
                        <div class="ufio-metric-change neutral"><?php echo $stats['avg_processing_time']; ?>ms avg</div>
                    </div>
                </div>

                <div class="ufio-metric-card info" data-metric="efficiency">
                    <div class="ufio-metric-icon">⚡</div>
                    <div class="ufio-metric-content">
                        <div class="ufio-metric-number" id="efficiency-score"><?php echo $stats['efficiency_score']; ?>%</div>
                        <div class="ufio-metric-label"><?php _e('Efficiency Score', 'ultra-featured-image-optimizer'); ?></div>
                        <div class="ufio-metric-change positive">+<?php echo $stats['efficiency_improvement']; ?>% vs last month</div>
                    </div>
                </div>
            </div>

            <!-- Main Content Grid -->
            <div class="ufio-dashboard-grid">
                <!-- Processing Overview -->
                <div class="ufio-dashboard-card">
                    <div class="ufio-card-header">
                        <h3><?php _e('Processing Overview', 'ultra-featured-image-optimizer'); ?></h3>
                        <div class="ufio-card-actions">
                            <button class="ufio-btn-icon" data-tooltip="Refresh">
                                <span class="dashicons dashicons-update"></span>
                            </button>
                        </div>
                    </div>
                    <div class="ufio-card-content">
                        <canvas id="processing-chart" width="400" height="200"></canvas>
                    </div>
                </div>

                <!-- Top Priority Posts -->
                <div class="ufio-dashboard-card">
                    <div class="ufio-card-header">
                        <h3><?php _e('Top Priority Posts', 'ultra-featured-image-optimizer'); ?></h3>
                        <span class="ufio-badge"><?php echo count($stats['top_priority_posts']); ?> posts</span>
                    </div>
                    <div class="ufio-card-content">
                        <div class="ufio-priority-list" id="priority-posts-list">
                            <?php foreach (array_slice($stats['top_priority_posts'], 0, 5) as $post): ?>
                            <div class="ufio-priority-item">
                                <div class="ufio-priority-score"><?php echo number_format($post['priority_score'], 1); ?></div>
                                <div class="ufio-priority-content">
                                    <div class="ufio-priority-title"><?php echo esc_html($post['post_title']); ?></div>
                                    <div class="ufio-priority-meta">
                                        Traffic: <?php echo number_format($post['traffic_weight'], 1); ?> | 
                                        SEO: <?php echo number_format($post['seo_potential'], 1); ?>
                                    </div>
                                </div>
                                <button class="ufio-btn-sm primary" data-post-id="<?php echo $post['post_id']; ?>">
                                    <?php _e('Process', 'ultra-featured-image-optimizer'); ?>
                                </button>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <div class="ufio-card-footer">
                            <a href="<?php echo admin_url('admin.php?page=ufio-queue'); ?>" class="ufio-btn secondary">
                                <?php _e('View All', 'ultra-featured-image-optimizer'); ?>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- System Performance -->
                <div class="ufio-dashboard-card">
                    <div class="ufio-card-header">
                        <h3><?php _e('System Performance', 'ultra-featured-image-optimizer'); ?></h3>
                        <div class="ufio-performance-indicator">
                            <span class="ufio-indicator excellent"></span>
                            <span><?php _e('Excellent', 'ultra-featured-image-optimizer'); ?></span>
                        </div>
                    </div>
                    <div class="ufio-card-content">
                        <div class="ufio-performance-metrics">
                            <div class="ufio-performance-item">
                                <div class="ufio-performance-label"><?php _e('API Response Time', 'ultra-featured-image-optimizer'); ?></div>
                                <div class="ufio-performance-value"><?php echo $stats['api_response_time']; ?>ms</div>
                                <div class="ufio-performance-bar">
                                    <div class="ufio-performance-fill" style="width: <?php echo min(100, (1000 - $stats['api_response_time']) / 10); ?>%"></div>
                                </div>
                            </div>
                            <div class="ufio-performance-item">
                                <div class="ufio-performance-label"><?php _e('Cache Hit Rate', 'ultra-featured-image-optimizer'); ?></div>
                                <div class="ufio-performance-value"><?php echo $stats['cache_hit_rate']; ?>%</div>
                                <div class="ufio-performance-bar">
                                    <div class="ufio-performance-fill" style="width: <?php echo $stats['cache_hit_rate']; ?>%"></div>
                                </div>
                            </div>
                            <div class="ufio-performance-item">
                                <div class="ufio-performance-label"><?php _e('Success Rate', 'ultra-featured-image-optimizer'); ?></div>
                                <div class="ufio-performance-value"><?php echo $stats['success_rate']; ?>%</div>
                                <div class="ufio-performance-bar">
                                    <div class="ufio-performance-fill" style="width: <?php echo $stats['success_rate']; ?>%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="ufio-dashboard-card">
                    <div class="ufio-card-header">
                        <h3><?php _e('Quick Actions', 'ultra-featured-image-optimizer'); ?></h3>
                    </div>
                    <div class="ufio-card-content">
                        <div class="ufio-action-grid">
                            <button class="ufio-action-btn" id="ufio-start-batch">
                                <span class="ufio-action-icon">🚀</span>
                                <span class="ufio-action-label"><?php _e('Start Batch Processing', 'ultra-featured-image-optimizer'); ?></span>
                            </button>
                            <button class="ufio-action-btn" id="ufio-optimize-cache">
                                <span class="ufio-action-icon">⚡</span>
                                <span class="ufio-action-label"><?php _e('Optimize Cache', 'ultra-featured-image-optimizer'); ?></span>
                            </button>
                            <button class="ufio-action-btn" id="ufio-recalc-priorities">
                                <span class="ufio-action-icon">🎯</span>
                                <span class="ufio-action-label"><?php _e('Recalculate Priorities', 'ultra-featured-image-optimizer'); ?></span>
                            </button>
                            <button class="ufio-action-btn" id="ufio-test-microservice">
                                <span class="ufio-action-icon">🔧</span>
                                <span class="ufio-action-label"><?php _e('Test Microservice', 'ultra-featured-image-optimizer'); ?></span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Real-time Activity Feed -->
            <div class="ufio-dashboard-card full-width">
                <div class="ufio-card-header">
                    <h3><?php _e('Real-time Activity', 'ultra-featured-image-optimizer'); ?></h3>
                    <div class="ufio-activity-controls">
                        <button class="ufio-btn-sm" id="ufio-pause-activity">
                            <span class="dashicons dashicons-controls-pause"></span>
                            <?php _e('Pause', 'ultra-featured-image-optimizer'); ?>
                        </button>
                        <button class="ufio-btn-sm" id="ufio-clear-activity">
                            <span class="dashicons dashicons-trash"></span>
                            <?php _e('Clear', 'ultra-featured-image-optimizer'); ?>
                        </button>
                    </div>
                </div>
                <div class="ufio-card-content">
                    <div class="ufio-activity-feed" id="ufio-activity-feed">
                        <!-- Activity items will be populated via JavaScript -->
                    </div>
                </div>
            </div>
        </div>
        <?php
    }
    
    /**
     * Get dashboard overview data
     */
    private function get_dashboard_overview() {
        $db = UFIO_Database::get_instance();
        $queue_manager = UFIO_Queue_Manager::get_instance();
        
        // Basic stats
        $total_posts = wp_count_posts()->publish;
        $posts_with_featured = $this->count_posts_with_featured();
        $coverage_percentage = $total_posts > 0 ? round(($posts_with_featured / $total_posts) * 100, 1) : 0;
        
        // Queue stats
        $queue_stats = $db->get_queue_stats();
        
        // Performance stats
        $performance = $db->get_performance_analytics(7);
        
        // Top priority posts
        $top_priority_posts = $queue_manager->get_top_priority_posts(10);
        
        return [
            'total_posts' => $total_posts,
            'posts_with_featured' => $posts_with_featured,
            'coverage_percentage' => $coverage_percentage,
            'queue_pending' => $queue_stats['pending'],
            'efficiency_score' => $this->calculate_efficiency_score($performance),
            'top_priority_posts' => $top_priority_posts,
            'api_response_time' => round($performance['avg_processing_time'] ?? 0),
            'cache_hit_rate' => $this->calculate_cache_hit_rate(),
            'success_rate' => round($performance['success_rate'] ?? 0),
            'posts_this_week' => $this->count_posts_this_week(),
            'coverage_improvement' => $this->calculate_coverage_improvement(),
            'efficiency_improvement' => $this->calculate_efficiency_improvement(),
            'avg_processing_time' => round($performance['avg_processing_time'] ?? 0)
        ];
    }
    
    /**
     * Count posts with featured images
     */
    private function count_posts_with_featured() {
        global $wpdb;
        
        return $wpdb->get_var(
            "SELECT COUNT(DISTINCT p.ID) 
             FROM {$wpdb->posts} p 
             INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id 
             WHERE p.post_status = 'publish' 
             AND p.post_type = 'post' 
             AND pm.meta_key = '_thumbnail_id' 
             AND pm.meta_value != ''"
        );
    }
    
    /**
     * Calculate efficiency score
     */
    private function calculate_efficiency_score($performance) {
        $base_score = 50;
        
        // Adjust based on success rate
        $success_bonus = ($performance['success_rate'] ?? 0) * 0.3;
        
        // Adjust based on processing time (lower is better)
        $time_penalty = min(20, ($performance['avg_processing_time'] ?? 1000) / 50);
        
        // Adjust based on cost efficiency
        $cost_efficiency = max(0, 30 - (($performance['total_cost'] ?? 0) * 1000));
        
        return round(min(100, max(0, $base_score + $success_bonus - $time_penalty + $cost_efficiency)));
    }
    
    /**
     * Calculate cache hit rate
     */
    private function calculate_cache_hit_rate() {
        $cache_manager = UFIO_Cache_Manager::get_instance();
        $stats = $cache_manager->get_cache_stats();
        
        // This would be calculated from actual cache metrics
        // For now, return a reasonable estimate
        return 85;
    }
    
    /**
     * Count posts created this week
     */
    private function count_posts_this_week() {
        global $wpdb;
        
        return $wpdb->get_var(
            "SELECT COUNT(*) FROM {$wpdb->posts} 
             WHERE post_type = 'post' 
             AND post_status = 'publish' 
             AND post_date >= DATE_SUB(NOW(), INTERVAL 7 DAY)"
        );
    }
    
    /**
     * Calculate coverage improvement
     */
    private function calculate_coverage_improvement() {
        // This would compare current month vs previous month
        // For now, return a reasonable estimate
        return 12;
    }
    
    /**
     * Calculate efficiency improvement
     */
    private function calculate_efficiency_improvement() {
        // This would compare current vs previous period
        // For now, return a reasonable estimate
        return 8;
    }
    
    /**
     * AJAX handler for dashboard stats
     */
    public function ajax_dashboard_stats() {
        check_ajax_referer('ufio_dashboard_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'Permission denied']);
        }
        
        $stats = $this->get_dashboard_overview();
        wp_send_json_success($stats);
    }
    
    /**
     * AJAX handler for queue stats
     */
    public function ajax_queue_stats() {
        check_ajax_referer('ufio_dashboard_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'Permission denied']);
        }
        
        $queue_manager = UFIO_Queue_Manager::get_instance();
        $stats = $queue_manager->get_queue_statistics();
        
        wp_send_json_success($stats);
    }
    
    /**
     * AJAX handler for performance metrics
     */
    public function ajax_performance_metrics() {
        check_ajax_referer('ufio_dashboard_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'Permission denied']);
        }
        
        $db = UFIO_Database::get_instance();
        $metrics = $db->get_performance_analytics(24); // Last 24 hours
        
        wp_send_json_success($metrics);
    }
    
    /**
     * AJAX handler for top priority posts
     */
    public function ajax_top_priority_posts() {
        check_ajax_referer('ufio_dashboard_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'Permission denied']);
        }
        
        $queue_manager = UFIO_Queue_Manager::get_instance();
        $posts = $queue_manager->get_top_priority_posts(20);
        
        wp_send_json_success($posts);
    }
    
    /**
     * AJAX handler for microservice status
     */
    public function ajax_microservice_status() {
        check_ajax_referer('ufio_dashboard_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'Permission denied']);
        }
        
        $microservice = UFIO_Microservice_Client::get_instance();
        $status = $microservice->get_service_status();
        
        wp_send_json_success($status);
    }
    
    /**
     * Render analytics page
     */
    public function render_analytics_page() {
        $performance = UFIO_Database::get_instance()->get_performance_analytics(30);
        ?>
        <div class="wrap ufio-dashboard-wrap">
            <div class="ufio-dashboard-header">
                <h1>📊 Performance Analytics</h1>
                <div class="ufio-header-actions">
                    <select id="ufio-analytics-period">
                        <option value="7">Last 7 Days</option>
                        <option value="30" selected>Last 30 Days</option>
                        <option value="90">Last 90 Days</option>
                    </select>
                    <button class="ufio-btn primary" id="ufio-export-analytics">
                        <span class="dashicons dashicons-download"></span>
                        Export Data
                    </button>
                </div>
            </div>

            <div class="ufio-metrics-grid">
                <div class="ufio-metric-card success">
                    <div class="ufio-metric-icon">⚡</div>
                    <div class="ufio-metric-content">
                        <div class="ufio-metric-number"><?php echo number_format($performance['total_events']); ?></div>
                        <div class="ufio-metric-label">Total Events</div>
                    </div>
                </div>
                <div class="ufio-metric-card info">
                    <div class="ufio-metric-icon">⏱️</div>
                    <div class="ufio-metric-content">
                        <div class="ufio-metric-number"><?php echo round($performance['avg_processing_time']); ?>ms</div>
                        <div class="ufio-metric-label">Avg Processing Time</div>
                    </div>
                </div>
                <div class="ufio-metric-card warning">
                    <div class="ufio-metric-icon">🎯</div>
                    <div class="ufio-metric-content">
                        <div class="ufio-metric-number"><?php echo round($performance['success_rate']); ?>%</div>
                        <div class="ufio-metric-label">Success Rate</div>
                    </div>
                </div>
                <div class="ufio-metric-card primary">
                    <div class="ufio-metric-icon">💰</div>
                    <div class="ufio-metric-content">
                        <div class="ufio-metric-number">$<?php echo number_format($performance['total_cost'], 2); ?></div>
                        <div class="ufio-metric-label">Total API Cost</div>
                    </div>
                </div>
            </div>

            <div class="ufio-dashboard-grid">
                <div class="ufio-dashboard-card">
                    <div class="ufio-card-header">
                        <h3>Processing Timeline</h3>
                    </div>
                    <div class="ufio-card-content">
                        <canvas id="analytics-timeline-chart" width="400" height="200"></canvas>
                    </div>
                </div>

                <div class="ufio-dashboard-card">
                    <div class="ufio-card-header">
                        <h3>API Usage</h3>
                    </div>
                    <div class="ufio-card-content">
                        <canvas id="analytics-api-chart" width="400" height="200"></canvas>
                    </div>
                </div>

                <div class="ufio-dashboard-card">
                    <div class="ufio-card-header">
                        <h3>Error Analysis</h3>
                    </div>
                    <div class="ufio-card-content">
                        <div class="ufio-error-list" id="error-analysis">
                            <!-- Error data will be loaded via AJAX -->
                        </div>
                    </div>
                </div>

                <div class="ufio-dashboard-card">
                    <div class="ufio-card-header">
                        <h3>Performance Trends</h3>
                    </div>
                    <div class="ufio-card-content">
                        <canvas id="analytics-performance-chart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Render queue management page
     */
    public function render_queue_page() {
        $queue_stats = UFIO_Database::get_instance()->get_queue_stats();
        $top_posts = UFIO_Queue_Manager::get_instance()->get_top_priority_posts(50);
        ?>
        <div class="wrap ufio-dashboard-wrap">
            <div class="ufio-dashboard-header">
                <h1>⏳ Queue Management</h1>
                <div class="ufio-header-actions">
                    <button class="ufio-btn primary" id="ufio-process-queue">
                        <span class="dashicons dashicons-controls-play"></span>
                        Process Queue
                    </button>
                    <button class="ufio-btn secondary" id="ufio-pause-queue">
                        <span class="dashicons dashicons-controls-pause"></span>
                        Pause Queue
                    </button>
                    <button class="ufio-btn warning" id="ufio-clear-failed">
                        <span class="dashicons dashicons-trash"></span>
                        Clear Failed
                    </button>
                </div>
            </div>

            <div class="ufio-metrics-grid">
                <div class="ufio-metric-card info">
                    <div class="ufio-metric-icon">📋</div>
                    <div class="ufio-metric-content">
                        <div class="ufio-metric-number"><?php echo number_format($queue_stats['pending']); ?></div>
                        <div class="ufio-metric-label">Pending</div>
                    </div>
                </div>
                <div class="ufio-metric-card warning">
                    <div class="ufio-metric-icon">🔄</div>
                    <div class="ufio-metric-content">
                        <div class="ufio-metric-number"><?php echo number_format($queue_stats['processing']); ?></div>
                        <div class="ufio-metric-label">Processing</div>
                    </div>
                </div>
                <div class="ufio-metric-card success">
                    <div class="ufio-metric-icon">✅</div>
                    <div class="ufio-metric-content">
                        <div class="ufio-metric-number"><?php echo number_format($queue_stats['completed']); ?></div>
                        <div class="ufio-metric-label">Completed</div>
                    </div>
                </div>
                <div class="ufio-metric-card error">
                    <div class="ufio-metric-icon">❌</div>
                    <div class="ufio-metric-content">
                        <div class="ufio-metric-number"><?php echo number_format($queue_stats['failed']); ?></div>
                        <div class="ufio-metric-label">Failed</div>
                    </div>
                </div>
            </div>

            <div class="ufio-dashboard-card full-width">
                <div class="ufio-card-header">
                    <h3>Priority Queue (Top 50 Posts)</h3>
                    <div class="ufio-queue-controls">
                        <button class="ufio-btn-sm primary" id="ufio-select-all">Select All</button>
                        <button class="ufio-btn-sm secondary" id="ufio-process-selected">Process Selected</button>
                        <button class="ufio-btn-sm warning" id="ufio-remove-selected">Remove Selected</button>
                    </div>
                </div>
                <div class="ufio-card-content">
                    <div class="ufio-queue-table-container">
                        <table class="ufio-queue-table">
                            <thead>
                                <tr>
                                    <th><input type="checkbox" id="select-all-checkbox"></th>
                                    <th>Priority</th>
                                    <th>Post Title</th>
                                    <th>Traffic Weight</th>
                                    <th>SEO Potential</th>
                                    <th>Status</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($top_posts as $post): ?>
                                <tr data-post-id="<?php echo $post['post_id']; ?>">
                                    <td><input type="checkbox" class="post-checkbox" value="<?php echo $post['post_id']; ?>"></td>
                                    <td><span class="ufio-priority-badge"><?php echo number_format($post['priority_score'], 1); ?></span></td>
                                    <td>
                                        <strong><?php echo esc_html($post['post_title']); ?></strong>
                                        <div class="ufio-post-meta">ID: <?php echo $post['post_id']; ?></div>
                                    </td>
                                    <td><?php echo number_format($post['traffic_weight'], 1); ?></td>
                                    <td><?php echo number_format($post['seo_potential'], 1); ?></td>
                                    <td><span class="ufio-status-badge <?php echo $post['status']; ?>"><?php echo ucfirst($post['status']); ?></span></td>
                                    <td><?php echo date('M j, Y', strtotime($post['created_at'])); ?></td>
                                    <td>
                                        <button class="ufio-btn-sm primary process-single" data-post-id="<?php echo $post['post_id']; ?>">Process</button>
                                        <button class="ufio-btn-sm secondary view-post" data-post-id="<?php echo $post['post_id']; ?>">View</button>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Render system status page
     */
    public function render_system_page() {
        $cache_stats = UFIO_Cache_Manager::get_instance()->get_cache_stats();
        $microservice_status = UFIO_Microservice_Client::get_instance()->get_service_status();
        ?>
        <div class="wrap ufio-dashboard-wrap">
            <div class="ufio-dashboard-header">
                <h1>🔧 System Status</h1>
                <div class="ufio-header-actions">
                    <button class="ufio-btn primary" id="ufio-system-refresh">
                        <span class="dashicons dashicons-update"></span>
                        Refresh Status
                    </button>
                    <button class="ufio-btn warning" id="ufio-clear-all-cache">
                        <span class="dashicons dashicons-trash"></span>
                        Clear All Cache
                    </button>
                </div>
            </div>

            <div class="ufio-dashboard-grid">
                <!-- WordPress Environment -->
                <div class="ufio-dashboard-card">
                    <div class="ufio-card-header">
                        <h3>WordPress Environment</h3>
                        <span class="ufio-status-indicator success">Healthy</span>
                    </div>
                    <div class="ufio-card-content">
                        <div class="ufio-system-info">
                            <div class="ufio-info-row">
                                <span class="ufio-info-label">WordPress Version:</span>
                                <span class="ufio-info-value"><?php echo get_bloginfo('version'); ?></span>
                            </div>
                            <div class="ufio-info-row">
                                <span class="ufio-info-label">PHP Version:</span>
                                <span class="ufio-info-value"><?php echo PHP_VERSION; ?></span>
                            </div>
                            <div class="ufio-info-row">
                                <span class="ufio-info-label">Memory Limit:</span>
                                <span class="ufio-info-value"><?php echo ini_get('memory_limit'); ?></span>
                            </div>
                            <div class="ufio-info-row">
                                <span class="ufio-info-label">Max Execution Time:</span>
                                <span class="ufio-info-value"><?php echo ini_get('max_execution_time'); ?>s</span>
                            </div>
                            <div class="ufio-info-row">
                                <span class="ufio-info-label">Upload Max Size:</span>
                                <span class="ufio-info-value"><?php echo ini_get('upload_max_filesize'); ?></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Cache System -->
                <div class="ufio-dashboard-card">
                    <div class="ufio-card-header">
                        <h3>Cache System</h3>
                        <span class="ufio-status-indicator <?php echo $cache_stats['redis_available'] ? 'success' : 'warning'; ?>">
                            <?php echo $cache_stats['redis_available'] ? 'Redis Active' : 'WordPress Cache'; ?>
                        </span>
                    </div>
                    <div class="ufio-card-content">
                        <div class="ufio-system-info">
                            <div class="ufio-info-row">
                                <span class="ufio-info-label">Redis Status:</span>
                                <span class="ufio-info-value <?php echo $cache_stats['redis_available'] ? 'success' : 'error'; ?>">
                                    <?php echo $cache_stats['redis_available'] ? 'Connected' : 'Disconnected'; ?>
                                </span>
                            </div>
                            <?php if ($cache_stats['redis_available'] && $cache_stats['redis_info']): ?>
                            <div class="ufio-info-row">
                                <span class="ufio-info-label">Memory Used:</span>
                                <span class="ufio-info-value"><?php echo $cache_stats['redis_info']['used_memory']; ?></span>
                            </div>
                            <div class="ufio-info-row">
                                <span class="ufio-info-label">Connected Clients:</span>
                                <span class="ufio-info-value"><?php echo $cache_stats['redis_info']['connected_clients']; ?></span>
                            </div>
                            <?php endif; ?>
                            <div class="ufio-info-row">
                                <span class="ufio-info-label">Object Cache:</span>
                                <span class="ufio-info-value <?php echo $cache_stats['wp_cache_enabled'] ? 'success' : 'warning'; ?>">
                                    <?php echo $cache_stats['wp_cache_enabled'] ? 'Enabled' : 'Disabled'; ?>
                                </span>
                            </div>
                            <div class="ufio-info-row">
                                <span class="ufio-info-label">Transients:</span>
                                <span class="ufio-info-value"><?php echo number_format($cache_stats['transient_count']); ?></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Microservice Status -->
                <div class="ufio-dashboard-card">
                    <div class="ufio-card-header">
                        <h3>Microservice</h3>
                        <span class="ufio-status-indicator <?php echo $microservice_status['status'] === 'healthy' ? 'success' : ($microservice_status['status'] === 'not_configured' ? 'info' : 'error'); ?>">
                            <?php echo ucfirst(str_replace('_', ' ', $microservice_status['status'])); ?>
                        </span>
                    </div>
                    <div class="ufio-card-content">
                        <div class="ufio-system-info">
                            <?php if ($microservice_status['status'] === 'not_configured'): ?>
                            <div class="ufio-info-message info">
                                <p>Microservice not configured. The plugin will use local processing.</p>
                                <p><strong>Benefits of microservice:</strong></p>
                                <ul>
                                    <li>100-1000× faster image processing</li>
                                    <li>CLIP embeddings for semantic search</li>
                                    <li>Vector-based image matching</li>
                                    <li>Reduced server load</li>
                                </ul>
                            </div>
                            <?php elseif ($microservice_status['status'] === 'healthy'): ?>
                            <div class="ufio-info-row">
                                <span class="ufio-info-label">Status:</span>
                                <span class="ufio-info-value success">Healthy & Responding</span>
                            </div>
                            <div class="ufio-info-row">
                                <span class="ufio-info-label">Response Time:</span>
                                <span class="ufio-info-value">< 100ms</span>
                            </div>
                            <div class="ufio-info-row">
                                <span class="ufio-info-label">Version:</span>
                                <span class="ufio-info-value"><?php echo $microservice_status['data']['version'] ?? 'Unknown'; ?></span>
                            </div>
                            <?php else: ?>
                            <div class="ufio-info-message error">
                                <p><strong>Connection Error:</strong> <?php echo esc_html($microservice_status['message']); ?></p>
                                <p>Check your microservice URL and API key in Settings.</p>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Database Status -->
                <div class="ufio-dashboard-card">
                    <div class="ufio-card-header">
                        <h3>Database</h3>
                        <span class="ufio-status-indicator success">Healthy</span>
                    </div>
                    <div class="ufio-card-content">
                        <div class="ufio-system-info">
                            <?php
                            global $wpdb;
                            $tables = [
                                'ufio_queue' => 'Processing Queue',
                                'ufio_batch_cache' => 'Batch Cache',
                                'ufio_image_cache' => 'Image Cache',
                                'ufio_analytics' => 'Analytics',
                                'ufio_microservice_queue' => 'Microservice Queue'
                            ];

                            foreach ($tables as $table => $label):
                                $full_table = $wpdb->prefix . $table;
                                $count = $wpdb->get_var("SELECT COUNT(*) FROM $full_table");
                            ?>
                            <div class="ufio-info-row">
                                <span class="ufio-info-label"><?php echo $label; ?>:</span>
                                <span class="ufio-info-value"><?php echo number_format($count); ?> records</span>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Actions -->
            <div class="ufio-dashboard-card full-width">
                <div class="ufio-card-header">
                    <h3>System Actions</h3>
                </div>
                <div class="ufio-card-content">
                    <div class="ufio-action-grid">
                        <button class="ufio-action-btn" id="ufio-test-gemini">
                            <span class="ufio-action-icon">🤖</span>
                            <span class="ufio-action-label">Test Gemini API</span>
                        </button>
                        <button class="ufio-action-btn" id="ufio-test-microservice">
                            <span class="ufio-action-icon">🚀</span>
                            <span class="ufio-action-label">Test Microservice</span>
                        </button>
                        <button class="ufio-action-btn" id="ufio-rebuild-cache">
                            <span class="ufio-action-icon">🔄</span>
                            <span class="ufio-action-label">Rebuild Cache</span>
                        </button>
                        <button class="ufio-action-btn" id="ufio-export-logs">
                            <span class="ufio-action-icon">📋</span>
                            <span class="ufio-action-label">Export Logs</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Render settings page
     */
    public function render_settings_page() {
        // Handle form submission
        if (isset($_POST['ufio_save_settings'])) {
            check_admin_referer('ufio_settings_nonce');
            $this->save_settings();
        }

        $options = get_option('ufio_options', []);
        ?>
        <div class="wrap ufio-dashboard-wrap">
            <div class="ufio-dashboard-header">
                <h1>⚙️ Settings</h1>
                <div class="ufio-header-actions">
                    <button class="ufio-btn secondary" id="ufio-reset-settings">
                        <span class="dashicons dashicons-undo"></span>
                        Reset to Defaults
                    </button>
                </div>
            </div>

            <form method="post" action="">
                <?php wp_nonce_field('ufio_settings_nonce'); ?>

                <div class="ufio-dashboard-grid">
                    <!-- API Configuration -->
                    <div class="ufio-dashboard-card">
                        <div class="ufio-card-header">
                            <h3>🤖 AI Configuration</h3>
                        </div>
                        <div class="ufio-card-content">
                            <div class="ufio-form-group">
                                <label for="gemini_api_key">Gemini API Key</label>
                                <input type="password" id="gemini_api_key" name="gemini_api_key"
                                       value="<?php echo esc_attr($options['gemini_api_key'] ?? ''); ?>"
                                       placeholder="Enter your Google Gemini API key">
                                <p class="ufio-form-help">
                                    Get your API key from <a href="https://makersuite.google.com/app/apikey" target="_blank">Google AI Studio</a>
                                </p>
                            </div>
                            <div class="ufio-form-group">
                                <button type="button" class="ufio-btn secondary" id="ufio-test-gemini-key">
                                    <span class="dashicons dashicons-yes"></span>
                                    Test API Key
                                </button>
                                <span id="gemini-test-result"></span>
                            </div>
                        </div>
                    </div>

                    <!-- Microservice Configuration -->
                    <div class="ufio-dashboard-card">
                        <div class="ufio-card-header">
                            <h3>🚀 Microservice Configuration</h3>
                        </div>
                        <div class="ufio-card-content">
                            <div class="ufio-form-group">
                                <label for="microservice_url">Microservice URL</label>
                                <input type="url" id="microservice_url" name="microservice_url"
                                       value="<?php echo esc_attr($options['microservice_url'] ?? ''); ?>"
                                       placeholder="https://your-microservice.run.app">
                                <p class="ufio-form-help">
                                    Optional: Deploy the Python microservice for 100× faster processing
                                </p>
                            </div>
                            <div class="ufio-form-group">
                                <label for="microservice_api_key">Microservice API Key</label>
                                <input type="password" id="microservice_api_key" name="microservice_api_key"
                                       value="<?php echo esc_attr($options['microservice_api_key'] ?? ''); ?>"
                                       placeholder="Enter your microservice API key">
                            </div>
                            <div class="ufio-form-group">
                                <button type="button" class="ufio-btn secondary" id="ufio-test-microservice-connection">
                                    <span class="dashicons dashicons-yes"></span>
                                    Test Connection
                                </button>
                                <span id="microservice-test-result"></span>
                            </div>
                        </div>
                    </div>

                    <!-- Redis Configuration -->
                    <div class="ufio-dashboard-card">
                        <div class="ufio-card-header">
                            <h3>⚡ Redis Cache Configuration</h3>
                        </div>
                        <div class="ufio-card-content">
                            <div class="ufio-form-group">
                                <label for="redis_host">Redis Host</label>
                                <input type="text" id="redis_host" name="redis_host"
                                       value="<?php echo esc_attr($options['redis_host'] ?? '127.0.0.1'); ?>">
                            </div>
                            <div class="ufio-form-group">
                                <label for="redis_port">Redis Port</label>
                                <input type="number" id="redis_port" name="redis_port"
                                       value="<?php echo esc_attr($options['redis_port'] ?? 6379); ?>">
                            </div>
                            <div class="ufio-form-group">
                                <label for="redis_password">Redis Password</label>
                                <input type="password" id="redis_password" name="redis_password"
                                       value="<?php echo esc_attr($options['redis_password'] ?? ''); ?>"
                                       placeholder="Leave empty if no password">
                            </div>
                            <div class="ufio-form-group">
                                <label for="redis_database">Redis Database</label>
                                <input type="number" id="redis_database" name="redis_database"
                                       value="<?php echo esc_attr($options['redis_database'] ?? 0); ?>" min="0" max="15">
                            </div>
                        </div>
                    </div>

                    <!-- Processing Settings -->
                    <div class="ufio-dashboard-card">
                        <div class="ufio-card-header">
                            <h3>⚙️ Processing Settings</h3>
                        </div>
                        <div class="ufio-card-content">
                            <div class="ufio-form-group">
                                <label class="ufio-checkbox-label">
                                    <input type="checkbox" name="auto_assign_featured" value="1"
                                           <?php checked($options['auto_assign_featured'] ?? true); ?>>
                                    <span class="ufio-checkbox-custom"></span>
                                    Auto-assign featured images to new posts
                                </label>
                            </div>
                            <div class="ufio-form-group">
                                <label class="ufio-checkbox-label">
                                    <input type="checkbox" name="enable_seo_optimization" value="1"
                                           <?php checked($options['enable_seo_optimization'] ?? true); ?>>
                                    <span class="ufio-checkbox-custom"></span>
                                    Enable SEO optimization for images
                                </label>
                            </div>
                            <div class="ufio-form-group">
                                <label class="ufio-checkbox-label">
                                    <input type="checkbox" name="background_processing" value="1"
                                           <?php checked($options['background_processing'] ?? true); ?>>
                                    <span class="ufio-checkbox-custom"></span>
                                    Enable background processing
                                </label>
                            </div>
                            <div class="ufio-form-group">
                                <label class="ufio-checkbox-label">
                                    <input type="checkbox" name="batch_processing" value="1"
                                           <?php checked($options['batch_processing'] ?? true); ?>>
                                    <span class="ufio-checkbox-custom"></span>
                                    Enable batch processing (Recommended)
                                </label>
                            </div>
                            <div class="ufio-form-group">
                                <label class="ufio-checkbox-label">
                                    <input type="checkbox" name="priority_processing" value="1"
                                           <?php checked($options['priority_processing'] ?? true); ?>>
                                    <span class="ufio-checkbox-custom"></span>
                                    Enable priority-based processing
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Performance Settings -->
                    <div class="ufio-dashboard-card">
                        <div class="ufio-card-header">
                            <h3>🚀 Performance Settings</h3>
                        </div>
                        <div class="ufio-card-content">
                            <div class="ufio-form-group">
                                <label for="max_images_per_batch">Batch Size</label>
                                <select id="max_images_per_batch" name="max_images_per_batch">
                                    <option value="20" <?php selected($options['max_images_per_batch'] ?? 50, 20); ?>>20 posts</option>
                                    <option value="50" <?php selected($options['max_images_per_batch'] ?? 50, 50); ?>>50 posts (Recommended)</option>
                                    <option value="100" <?php selected($options['max_images_per_batch'] ?? 50, 100); ?>>100 posts</option>
                                </select>
                                <p class="ufio-form-help">Number of posts to process in each batch</p>
                            </div>
                            <div class="ufio-form-group">
                                <label for="batch_timeout">Batch Timeout (ms)</label>
                                <input type="number" id="batch_timeout" name="batch_timeout"
                                       value="<?php echo esc_attr($options['batch_timeout'] ?? 500); ?>"
                                       min="100" max="5000" step="100">
                                <p class="ufio-form-help">How long to wait before processing a partial batch</p>
                            </div>
                            <div class="ufio-form-group">
                                <label for="image_quality">Image Quality (%)</label>
                                <input type="range" id="image_quality" name="image_quality"
                                       value="<?php echo esc_attr($options['image_quality'] ?? 85); ?>"
                                       min="60" max="100" step="5">
                                <span id="quality-value"><?php echo $options['image_quality'] ?? 85; ?>%</span>
                                <p class="ufio-form-help">Higher quality = larger file sizes</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="ufio-settings-footer">
                    <button type="submit" name="ufio_save_settings" class="ufio-btn primary large">
                        <span class="dashicons dashicons-yes"></span>
                        Save Settings
                    </button>
                    <button type="button" class="ufio-btn secondary large" id="ufio-test-all-connections">
                        <span class="dashicons dashicons-admin-tools"></span>
                        Test All Connections
                    </button>
                </div>
            </form>
        </div>
        <?php
    }

    /**
     * Save settings
     */
    private function save_settings() {
        $options = [
            'gemini_api_key' => sanitize_text_field($_POST['gemini_api_key'] ?? ''),
            'microservice_url' => esc_url_raw($_POST['microservice_url'] ?? ''),
            'microservice_api_key' => sanitize_text_field($_POST['microservice_api_key'] ?? ''),
            'redis_host' => sanitize_text_field($_POST['redis_host'] ?? '127.0.0.1'),
            'redis_port' => intval($_POST['redis_port'] ?? 6379),
            'redis_password' => sanitize_text_field($_POST['redis_password'] ?? ''),
            'redis_database' => intval($_POST['redis_database'] ?? 0),
            'auto_assign_featured' => isset($_POST['auto_assign_featured']),
            'enable_seo_optimization' => isset($_POST['enable_seo_optimization']),
            'background_processing' => isset($_POST['background_processing']),
            'batch_processing' => isset($_POST['batch_processing']),
            'priority_processing' => isset($_POST['priority_processing']),
            'max_images_per_batch' => intval($_POST['max_images_per_batch'] ?? 50),
            'batch_timeout' => intval($_POST['batch_timeout'] ?? 500),
            'image_quality' => intval($_POST['image_quality'] ?? 85),
        ];

        update_option('ufio_options', $options);

        echo '<div class="notice notice-success"><p>Settings saved successfully!</p></div>';
    }

    /**
     * Render post URLs page
     */
    public function render_post_urls_page() {
        ?>
        <div class="wrap ufio-dashboard-wrap">
            <div class="ufio-dashboard-header">
                <h1>📄 Post URLs & Image Analysis</h1>
                <div class="ufio-header-actions">
                    <button class="ufio-btn primary" id="ufio-refresh-post-list">
                        <span class="dashicons dashicons-update"></span>
                        Refresh List
                    </button>
                    <button class="ufio-btn secondary" id="ufio-export-post-data">
                        <span class="dashicons dashicons-download"></span>
                        Export Data
                    </button>
                </div>
            </div>

            <!-- Filters -->
            <div class="ufio-dashboard-card">
                <div class="ufio-card-header">
                    <h3>🔍 Filters & Search</h3>
                </div>
                <div class="ufio-card-content">
                    <div class="ufio-filters-grid">
                        <div class="ufio-filter-group">
                            <label for="post-type-filter">Post Type:</label>
                            <select id="post-type-filter">
                                <option value="">All Types</option>
                                <option value="post">Posts</option>
                                <option value="page">Pages</option>
                            </select>
                        </div>
                        <div class="ufio-filter-group">
                            <label for="image-count-filter">Image Count:</label>
                            <select id="image-count-filter">
                                <option value="">All Posts</option>
                                <option value="0">No Images</option>
                                <option value="1-3">1-3 Images</option>
                                <option value="4-10">4-10 Images</option>
                                <option value="10+">10+ Images</option>
                            </select>
                        </div>
                        <div class="ufio-filter-group">
                            <label for="featured-image-filter">Featured Image:</label>
                            <select id="featured-image-filter">
                                <option value="">All Posts</option>
                                <option value="yes">Has Featured Image</option>
                                <option value="no">No Featured Image</option>
                            </select>
                        </div>
                        <div class="ufio-filter-group">
                            <label for="search-posts">Search:</label>
                            <input type="text" id="search-posts" placeholder="Search by title or URL...">
                        </div>
                        <div class="ufio-filter-group">
                            <button class="ufio-btn primary" id="ufio-apply-filters">Apply Filters</button>
                            <button class="ufio-btn secondary" id="ufio-clear-filters">Clear</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Posts Table -->
            <div class="ufio-dashboard-card full-width">
                <div class="ufio-card-header">
                    <h3>📋 Blog Posts Analysis</h3>
                    <div class="ufio-table-controls">
                        <span id="posts-count">Loading...</span>
                        <button class="ufio-btn-sm primary" id="ufio-select-all-posts">Select All</button>
                        <button class="ufio-btn-sm warning" id="ufio-bulk-insert-images">Bulk Insert Images</button>
                    </div>
                </div>
                <div class="ufio-card-content">
                    <div class="ufio-table-container">
                        <table class="ufio-posts-table" id="posts-table">
                            <thead>
                                <tr>
                                    <th><input type="checkbox" id="select-all-posts-checkbox"></th>
                                    <th>Post Title</th>
                                    <th>URL</th>
                                    <th>Type</th>
                                    <th>Images in Content</th>
                                    <th>Featured Image</th>
                                    <th>Word Count</th>
                                    <th>Last Modified</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="posts-table-body">
                                <tr>
                                    <td colspan="9" class="ufio-loading-row">
                                        <div class="ufio-loading-spinner"></div>
                                        Loading posts...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="ufio-table-pagination" id="posts-pagination">
                        <!-- Pagination will be loaded via AJAX -->
                    </div>
                </div>
            </div>

            <!-- Image Insertion Modal -->
            <div id="ufio-image-insertion-modal" class="ufio-modal" style="display: none;">
                <div class="ufio-modal-content">
                    <div class="ufio-modal-header">
                        <h3>🖼️ Automatic Image Insertion</h3>
                        <button class="ufio-modal-close">&times;</button>
                    </div>
                    <div class="ufio-modal-body">
                        <div id="ufio-insertion-preview">
                            <!-- Content will be loaded dynamically -->
                        </div>
                        <div class="ufio-insertion-options">
                            <h4>Insertion Options:</h4>
                            <label class="ufio-checkbox-label">
                                <input type="checkbox" id="insert-after-intro" checked>
                                <span class="ufio-checkbox-custom"></span>
                                Insert after introduction paragraph
                            </label>
                            <label class="ufio-checkbox-label">
                                <input type="checkbox" id="insert-before-headings" checked>
                                <span class="ufio-checkbox-custom"></span>
                                Insert before major headings (H2, H3)
                            </label>
                            <label class="ufio-checkbox-label">
                                <input type="checkbox" id="insert-in-long-sections">
                                <span class="ufio-checkbox-custom"></span>
                                Insert in long sections (500+ words)
                            </label>
                            <label class="ufio-checkbox-label">
                                <input type="checkbox" id="optimize-alt-text" checked>
                                <span class="ufio-checkbox-custom"></span>
                                Generate SEO-optimized alt text
                            </label>
                        </div>
                    </div>
                    <div class="ufio-modal-footer">
                        <button class="ufio-btn secondary" id="ufio-cancel-insertion">Cancel</button>
                        <button class="ufio-btn primary" id="ufio-confirm-insertion">Insert Images</button>
                    </div>
                </div>
            </div>
        </div>

        <style>
        .ufio-filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            align-items: end;
        }

        .ufio-filter-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .ufio-filter-group label {
            font-weight: 600;
            color: var(--ufio-gray-700);
        }

        .ufio-filter-group input,
        .ufio-filter-group select {
            padding: 0.5rem;
            border: 2px solid var(--ufio-gray-300);
            border-radius: 6px;
            transition: var(--ufio-transition);
        }

        .ufio-filter-group input:focus,
        .ufio-filter-group select:focus {
            border-color: var(--ufio-primary);
            outline: none;
        }

        .ufio-posts-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .ufio-posts-table th,
        .ufio-posts-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid var(--ufio-gray-200);
        }

        .ufio-posts-table th {
            background: var(--ufio-gray-50);
            font-weight: 600;
            color: var(--ufio-gray-700);
        }

        .ufio-posts-table tr:hover {
            background: var(--ufio-gray-50);
        }

        .ufio-post-url {
            color: var(--ufio-primary);
            text-decoration: none;
            font-size: 0.875rem;
        }

        .ufio-post-url:hover {
            text-decoration: underline;
        }

        .ufio-image-count {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.25rem 0.75rem;
            background: var(--ufio-gray-100);
            border-radius: 12px;
            font-size: 0.875rem;
            font-weight: 600;
        }

        .ufio-image-count.no-images {
            background: rgba(245, 101, 101, 0.1);
            color: var(--ufio-error);
        }

        .ufio-image-count.has-images {
            background: rgba(72, 187, 120, 0.1);
            color: var(--ufio-success);
        }

        .ufio-featured-status {
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .ufio-featured-status.has-featured {
            background: rgba(72, 187, 120, 0.1);
            color: var(--ufio-success);
        }

        .ufio-featured-status.no-featured {
            background: rgba(245, 101, 101, 0.1);
            color: var(--ufio-error);
        }

        .ufio-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 999999;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .ufio-modal-content {
            background: var(--ufio-white);
            border-radius: var(--ufio-border-radius);
            max-width: 800px;
            width: 90%;
            max-height: 90%;
            overflow-y: auto;
        }

        .ufio-modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.5rem;
            border-bottom: 1px solid var(--ufio-gray-200);
        }

        .ufio-modal-header h3 {
            margin: 0;
        }

        .ufio-modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--ufio-gray-500);
        }

        .ufio-modal-body {
            padding: 1.5rem;
        }

        .ufio-modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 1rem;
            padding: 1.5rem;
            border-top: 1px solid var(--ufio-gray-200);
        }

        .ufio-loading-spinner {
            width: 20px;
            height: 20px;
            border: 2px solid var(--ufio-gray-300);
            border-top: 2px solid var(--ufio-primary);
            border-radius: 50%;
            animation: ufio-spin 1s linear infinite;
            display: inline-block;
            margin-right: 0.5rem;
        }

        .ufio-loading-row {
            text-align: center;
            padding: 2rem;
            color: var(--ufio-gray-600);
        }
        </style>
        <?php
    }

    /**
     * AJAX handler for getting post URLs
     */
    public function ajax_get_post_urls() {
        check_ajax_referer('ufio_dashboard_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'Permission denied']);
        }

        $page = intval($_POST['page'] ?? 1);
        $per_page = intval($_POST['per_page'] ?? 20);
        $post_type = sanitize_text_field($_POST['post_type'] ?? '');
        $image_count_filter = sanitize_text_field($_POST['image_count_filter'] ?? '');
        $featured_filter = sanitize_text_field($_POST['featured_filter'] ?? '');
        $search = sanitize_text_field($_POST['search'] ?? '');

        $args = [
            'post_type' => $post_type ?: ['post', 'page'],
            'post_status' => 'publish',
            'posts_per_page' => $per_page,
            'paged' => $page,
            'orderby' => 'modified',
            'order' => 'DESC'
        ];

        // Add search
        if ($search) {
            $args['s'] = $search;
        }

        // Add featured image filter
        if ($featured_filter === 'yes') {
            $args['meta_query'] = [
                [
                    'key' => '_thumbnail_id',
                    'compare' => 'EXISTS'
                ]
            ];
        } elseif ($featured_filter === 'no') {
            $args['meta_query'] = [
                [
                    'key' => '_thumbnail_id',
                    'compare' => 'NOT EXISTS'
                ]
            ];
        }

        $query = new WP_Query($args);
        $posts_data = [];

        foreach ($query->posts as $post) {
            $content = $post->post_content;
            $image_count = $this->count_images_in_content($content);
            $word_count = str_word_count(wp_strip_all_tags($content));
            $featured_image = get_post_thumbnail_id($post->ID);

            // Apply image count filter
            if ($image_count_filter) {
                $skip = false;
                switch ($image_count_filter) {
                    case '0':
                        if ($image_count > 0) $skip = true;
                        break;
                    case '1-3':
                        if ($image_count < 1 || $image_count > 3) $skip = true;
                        break;
                    case '4-10':
                        if ($image_count < 4 || $image_count > 10) $skip = true;
                        break;
                    case '10+':
                        if ($image_count < 10) $skip = true;
                        break;
                }
                if ($skip) continue;
            }

            $posts_data[] = [
                'id' => $post->ID,
                'title' => $post->post_title,
                'url' => get_permalink($post->ID),
                'edit_url' => get_edit_post_link($post->ID),
                'type' => $post->post_type,
                'image_count' => $image_count,
                'has_featured' => !empty($featured_image),
                'word_count' => $word_count,
                'modified' => $post->post_modified,
                'can_insert_images' => $this->can_insert_images($post)
            ];
        }

        wp_send_json_success([
            'posts' => $posts_data,
            'total' => $query->found_posts,
            'pages' => $query->max_num_pages,
            'current_page' => $page
        ]);
    }

    /**
     * AJAX handler for inserting content images
     */
    public function ajax_insert_content_image() {
        check_ajax_referer('ufio_dashboard_nonce', 'nonce');

        if (!current_user_can('edit_posts')) {
            wp_send_json_error(['message' => 'Permission denied']);
        }

        $post_id = intval($_POST['post_id']);
        $options = [
            'insert_after_intro' => isset($_POST['insert_after_intro']),
            'insert_before_headings' => isset($_POST['insert_before_headings']),
            'insert_in_long_sections' => isset($_POST['insert_in_long_sections']),
            'optimize_alt_text' => isset($_POST['optimize_alt_text'])
        ];

        $post = get_post($post_id);
        if (!$post) {
            wp_send_json_error(['message' => 'Post not found']);
        }

        try {
            $result = $this->insert_images_in_content($post, $options);

            if ($result['success']) {
                wp_send_json_success([
                    'message' => sprintf('Successfully inserted %d images', $result['inserted_count']),
                    'inserted_count' => $result['inserted_count'],
                    'insertion_points' => $result['insertion_points']
                ]);
            } else {
                wp_send_json_error(['message' => $result['message']]);
            }

        } catch (Exception $e) {
            wp_send_json_error(['message' => 'Error: ' . $e->getMessage()]);
        }
    }

    /**
     * Count images in post content
     */
    private function count_images_in_content($content) {
        // Count img tags
        $img_count = preg_match_all('/<img[^>]+>/i', $content);

        // Count gallery shortcodes
        $gallery_count = preg_match_all('/\[gallery[^\]]*\]/i', $content);

        // Count WordPress image blocks
        $block_count = preg_match_all('/<!-- wp:image[^>]*-->/', $content);

        return $img_count + $gallery_count + $block_count;
    }

    /**
     * Check if we can insert images in this post
     */
    private function can_insert_images($post) {
        $content = $post->post_content;
        $word_count = str_word_count(wp_strip_all_tags($content));

        // Need at least 300 words and some headings or paragraphs
        return $word_count >= 300 && (
            strpos($content, '<h2') !== false ||
            strpos($content, '<h3') !== false ||
            substr_count($content, '<p>') >= 3
        );
    }

    /**
     * Insert images in post content intelligently
     */
    private function insert_images_in_content($post, $options) {
        $content = $post->post_content;
        $inserted_count = 0;
        $insertion_points = [];

        // Get relevant images from media library
        $relevant_images = $this->find_relevant_images($post);

        if (empty($relevant_images)) {
            return [
                'success' => false,
                'message' => 'No relevant images found in media library'
            ];
        }

        // Simple insertion strategy - insert after first paragraph and before major headings
        $paragraphs = explode('</p>', $content);
        $modified_content = '';
        $image_index = 0;

        foreach ($paragraphs as $i => $paragraph) {
            $modified_content .= $paragraph;

            // Add closing </p> tag back (except for last item)
            if ($i < count($paragraphs) - 1) {
                $modified_content .= '</p>';
            }

            // Insert image after first paragraph
            if ($options['insert_after_intro'] && $i === 0 && $image_index < count($relevant_images)) {
                $image_html = $this->generate_image_html($relevant_images[$image_index], $post, $options);
                $modified_content .= "\n\n" . $image_html . "\n\n";
                $insertion_points[] = ['position' => 'After introduction', 'image_id' => $relevant_images[$image_index]->ID];
                $image_index++;
                $inserted_count++;
            }

            // Insert before headings (simple detection)
            if ($options['insert_before_headings'] && $image_index < count($relevant_images)) {
                $next_paragraph = $paragraphs[$i + 1] ?? '';
                if (strpos($next_paragraph, '<h2') !== false || strpos($next_paragraph, '<h3') !== false) {
                    $image_html = $this->generate_image_html($relevant_images[$image_index], $post, $options);
                    $modified_content .= "\n\n" . $image_html . "\n\n";
                    $insertion_points[] = ['position' => 'Before heading', 'image_id' => $relevant_images[$image_index]->ID];
                    $image_index++;
                    $inserted_count++;
                }
            }

            // Limit to 3 images max
            if ($inserted_count >= 3) break;
        }

        // Update post content if images were inserted
        if ($inserted_count > 0) {
            wp_update_post([
                'ID' => $post->ID,
                'post_content' => $modified_content
            ]);
        }

        return [
            'success' => true,
            'inserted_count' => $inserted_count,
            'insertion_points' => $insertion_points
        ];
    }

    /**
     * Find relevant images for a post
     */
    private function find_relevant_images($post) {
        // Extract keywords from post title and content
        $keywords = $this->extract_keywords_from_post($post);

        // Search media library for relevant images
        $args = [
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'post_status' => 'inherit',
            'posts_per_page' => 5,
            'orderby' => 'date',
            'order' => 'DESC'
        ];

        // Search by keywords if available
        if (!empty($keywords)) {
            $args['s'] = implode(' ', array_slice($keywords, 0, 3));
        }

        $images = get_posts($args);

        // If no images found with keywords, get recent images
        if (empty($images)) {
            unset($args['s']);
            $images = get_posts($args);
        }

        return $images;
    }

    /**
     * Extract keywords from post
     */
    private function extract_keywords_from_post($post) {
        $text = $post->post_title . ' ' . wp_strip_all_tags($post->post_content);
        $words = str_word_count($text, 1);

        // Remove common words
        $stop_words = ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'];

        $keywords = array_filter($words, function($word) use ($stop_words) {
            return strlen($word) > 3 && !in_array(strtolower($word), $stop_words);
        });

        // Get most frequent words
        $word_counts = array_count_values(array_map('strtolower', $keywords));
        arsort($word_counts);

        return array_keys(array_slice($word_counts, 0, 5));
    }

    /**
     * Generate HTML for inserted image
     */
    private function generate_image_html($image, $post, $options) {
        $image_url = wp_get_attachment_image_url($image->ID, 'large');
        $alt_text = get_post_meta($image->ID, '_wp_attachment_image_alt', true);

        // Generate optimized alt text if requested
        if ($options['optimize_alt_text'] && empty($alt_text)) {
            $keywords = $this->extract_keywords_from_post($post);
            $alt_text = sprintf('%s related to %s', $keywords[0] ?? 'Image', $post->post_title);
            update_post_meta($image->ID, '_wp_attachment_image_alt', $alt_text);
        }

        return sprintf(
            '<figure class="wp-block-image size-large"><img src="%s" alt="%s" class="wp-image-%d"/></figure>',
            esc_url($image_url),
            esc_attr($alt_text ?: 'Related image'),
            $image->ID
        );
    }
}
