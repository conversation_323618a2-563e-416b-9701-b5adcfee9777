<?php
/**
 * UFIO Batch Processor
 * Handles batched Gemini API calls with intelligent caching and delta updates
 */

if (!defined('ABSPATH')) {
    exit;
}

class UFIO_Batch_Processor {
    
    private static $instance = null;
    private $batch_queue = [];
    private $batch_size = 50;
    private $buffer_timeout = 500; // milliseconds
    private $buffer_timer = null;
    private $api_key = '';
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        $options = get_option('ufio_options', []);
        $this->api_key = $options['gemini_api_key'] ?? '';
        
        // Schedule batch processing
        add_action('ufio_process_batch', [$this, 'process_batch']);
        add_action('wp_ajax_ufio_flush_batch', [$this, 'flush_batch_ajax']);
    }
    
    /**
     * Add post to batch queue for analysis
     */
    public function queue_post_analysis($post_id, $priority = 5) {
        $post = get_post($post_id);
        if (!$post) {
            return false;
        }
        
        // Check if already cached
        if ($this->get_cached_analysis($post_id)) {
            return true;
        }
        
        $this->batch_queue[] = [
            'post_id' => $post_id,
            'title' => $post->post_title,
            'content' => wp_strip_all_tags($post->post_content),
            'modified' => $post->post_modified,
            'priority' => $priority,
            'queued_at' => microtime(true)
        ];
        
        // Auto-flush if batch is full
        if (count($this->batch_queue) >= $this->batch_size) {
            $this->flush_batch();
        } else {
            // Set timer for buffer timeout
            $this->schedule_buffer_flush();
        }
        
        return true;
    }
    
    /**
     * Schedule buffer flush after timeout
     */
    private function schedule_buffer_flush() {
        if ($this->buffer_timer) {
            wp_clear_scheduled_hook('ufio_flush_batch_buffer');
        }
        
        wp_schedule_single_event(time() + 1, 'ufio_flush_batch_buffer');
        add_action('ufio_flush_batch_buffer', [$this, 'flush_batch']);
    }
    
    /**
     * Flush current batch to Gemini API
     */
    public function flush_batch() {
        if (empty($this->batch_queue)) {
            return;
        }
        
        $start_time = microtime(true);
        $batch_id = uniqid('batch_', true);
        
        try {
            // Check for existing cache
            $cache_result = $this->check_batch_cache($this->batch_queue);
            if ($cache_result) {
                $this->process_cached_batch($cache_result);
                return;
            }
            
            // Process with Gemini API
            $response = $this->call_gemini_batch_api($this->batch_queue);
            
            if ($response) {
                $this->cache_batch_response($this->batch_queue, $response);
                $this->process_batch_response($response);
                
                // Log success
                $this->log_batch_analytics($batch_id, $start_time, count($this->batch_queue), true);
            } else {
                throw new Exception('Gemini API returned empty response');
            }
            
        } catch (Exception $e) {
            error_log('UFIO Batch Processing Error: ' . $e->getMessage());
            $this->log_batch_analytics($batch_id, $start_time, count($this->batch_queue), false, $e->getMessage());
            
            // Fallback to individual processing
            $this->fallback_individual_processing();
        }
        
        // Clear the queue
        $this->batch_queue = [];
    }
    
    /**
     * Check if batch results are cached
     */
    private function check_batch_cache($batch) {
        global $wpdb;
        
        $post_ids = array_column($batch, 'post_id');
        $content_hash = $this->generate_content_hash($batch);
        $cache_key = md5(implode(',', $post_ids) . $content_hash);
        
        $cache_table = $wpdb->prefix . 'ufio_batch_cache';
        $cached = $wpdb->get_row($wpdb->prepare(
            "SELECT ai_response FROM $cache_table 
             WHERE cache_key = %s AND expires_at > NOW()",
            $cache_key
        ));
        
        if ($cached) {
            $this->log_analytics('cache_hit', null, $cache_key);
            return json_decode($cached->ai_response, true);
        }
        
        $this->log_analytics('cache_miss', null, $cache_key);
        return false;
    }
    
    /**
     * Generate content hash for cache key
     */
    private function generate_content_hash($batch) {
        $content_string = '';
        foreach ($batch as $item) {
            $content_string .= $item['post_id'] . $item['modified'] . substr(md5($item['content']), 0, 8);
        }
        return md5($content_string);
    }
    
    /**
     * Call Gemini API with batch of posts
     */
    private function call_gemini_batch_api($batch) {
        if (empty($this->api_key)) {
            throw new Exception('Gemini API key not configured');
        }
        
        $prompt = $this->build_batch_prompt($batch);
        
        $response = wp_remote_post(
            'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent?key=' . $this->api_key,
            [
                'headers' => ['Content-Type' => 'application/json'],
                'body' => json_encode([
                    'contents' => [
                        ['parts' => [['text' => $prompt]]]
                    ],
                    'generationConfig' => [
                        'temperature' => 0.1,
                        'maxOutputTokens' => 8192
                    ]
                ]),
                'timeout' => 60
            ]
        );
        
        if (is_wp_error($response)) {
            throw new Exception('API request failed: ' . $response->get_error_message());
        }
        
        $status_code = wp_remote_retrieve_response_code($response);
        if ($status_code !== 200) {
            throw new Exception('API returned error code: ' . $status_code);
        }
        
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);
        
        if (!isset($data['candidates'][0]['content']['parts'][0]['text'])) {
            throw new Exception('Invalid API response format');
        }
        
        $ai_response = $data['candidates'][0]['content']['parts'][0]['text'];
        
        // Extract JSON from response
        preg_match('/\[.*\]/s', $ai_response, $json_matches);
        if (empty($json_matches[0])) {
            throw new Exception('No valid JSON found in API response');
        }
        
        $parsed_response = json_decode($json_matches[0], true);
        if (!$parsed_response) {
            throw new Exception('Failed to parse JSON response');
        }
        
        return $parsed_response;
    }
    
    /**
     * Build optimized batch prompt for Gemini
     */
    private function build_batch_prompt($batch) {
        $prompt = "Analyze the following blog posts and return a JSON array with analysis for each post. ";
        $prompt .= "For each post, provide: post_id, keywords (array of 5-8 SEO keywords), tone (professional/casual/technical/creative), ";
        $prompt .= "suggested_image_types (array of 3-5 image types), seo_potential (0-100 score), and target_audience.\n\n";
        $prompt .= "Return ONLY a valid JSON array, no other text:\n\n";
        
        foreach ($batch as $index => $item) {
            $prompt .= "Post " . ($index + 1) . " (ID: {$item['post_id']}):\n";
            $prompt .= "Title: " . substr($item['title'], 0, 200) . "\n";
            $prompt .= "Content: " . substr($item['content'], 0, 1000) . "\n\n";
        }
        
        $prompt .= "Expected JSON format:\n";
        $prompt .= '[{"post_id": 123, "keywords": ["keyword1", "keyword2"], "tone": "professional", ';
        $prompt .= '"suggested_image_types": ["business", "professional"], "seo_potential": 85, "target_audience": "professionals"}]';
        
        return $prompt;
    }
    
    /**
     * Cache batch response
     */
    private function cache_batch_response($batch, $response) {
        global $wpdb;
        
        $post_ids = array_column($batch, 'post_id');
        $content_hash = $this->generate_content_hash($batch);
        $cache_key = md5(implode(',', $post_ids) . $content_hash);
        
        $cache_table = $wpdb->prefix . 'ufio_batch_cache';
        
        $wpdb->replace($cache_table, [
            'cache_key' => $cache_key,
            'post_ids' => json_encode($post_ids),
            'content_hash' => $content_hash,
            'ai_response' => json_encode($response),
            'expires_at' => date('Y-m-d H:i:s', strtotime('+24 hours'))
        ]);
    }
    
    /**
     * Process batch response and store individual results
     */
    private function process_batch_response($response) {
        foreach ($response as $analysis) {
            if (isset($analysis['post_id'])) {
                $this->store_post_analysis($analysis['post_id'], $analysis);
            }
        }
    }
    
    /**
     * Process cached batch results
     */
    private function process_cached_batch($cached_response) {
        $this->process_batch_response($cached_response);
    }
    
    /**
     * Store individual post analysis
     */
    private function store_post_analysis($post_id, $analysis) {
        $cache_key = 'ufio_analysis_' . $post_id;
        
        // Store in WordPress cache
        wp_cache_set($cache_key, $analysis, 'ufio', DAY_IN_SECONDS);
        
        // Store in transient as fallback
        set_transient($cache_key, $analysis, DAY_IN_SECONDS);
        
        // Store in post meta for persistence
        update_post_meta($post_id, '_ufio_ai_analysis', $analysis);
        update_post_meta($post_id, '_ufio_analysis_timestamp', time());
    }
    
    /**
     * Get cached analysis for a post
     */
    public function get_cached_analysis($post_id) {
        $cache_key = 'ufio_analysis_' . $post_id;
        
        // Try WordPress object cache first
        $analysis = wp_cache_get($cache_key, 'ufio');
        if ($analysis !== false) {
            return $analysis;
        }
        
        // Try transient
        $analysis = get_transient($cache_key);
        if ($analysis !== false) {
            wp_cache_set($cache_key, $analysis, 'ufio', DAY_IN_SECONDS);
            return $analysis;
        }
        
        // Try post meta
        $analysis = get_post_meta($post_id, '_ufio_ai_analysis', true);
        $timestamp = get_post_meta($post_id, '_ufio_analysis_timestamp', true);
        
        // Check if analysis is still fresh (24 hours)
        if ($analysis && $timestamp && (time() - $timestamp) < DAY_IN_SECONDS) {
            wp_cache_set($cache_key, $analysis, 'ufio', DAY_IN_SECONDS);
            set_transient($cache_key, $analysis, DAY_IN_SECONDS);
            return $analysis;
        }
        
        return false;
    }
    
    /**
     * Fallback to individual processing if batch fails
     */
    private function fallback_individual_processing() {
        foreach ($this->batch_queue as $item) {
            // Queue for individual processing
            UFIO_Queue_Manager::get_instance()->add_to_queue($item['post_id'], $item['priority']);
        }
    }
    
    /**
     * Log batch analytics
     */
    private function log_batch_analytics($batch_id, $start_time, $batch_size, $success, $error = null) {
        $processing_time = (microtime(true) - $start_time) * 1000; // Convert to milliseconds
        
        $this->log_analytics('batch_process', null, $batch_id, [
            'batch_size' => $batch_size,
            'processing_time_ms' => $processing_time,
            'success' => $success,
            'error' => $error
        ]);
    }
    
    /**
     * Log analytics event
     */
    private function log_analytics($event_type, $post_id = null, $batch_id = null, $metadata = []) {
        global $wpdb;
        
        $analytics_table = $wpdb->prefix . 'ufio_analytics';
        
        $wpdb->insert($analytics_table, [
            'event_type' => $event_type,
            'post_id' => $post_id,
            'batch_id' => $batch_id,
            'processing_time_ms' => $metadata['processing_time_ms'] ?? null,
            'api_tokens_used' => $metadata['api_tokens_used'] ?? null,
            'api_cost_usd' => $metadata['api_cost_usd'] ?? null,
            'memory_usage_mb' => round(memory_get_usage(true) / 1024 / 1024, 2),
            'success' => $metadata['success'] ?? true,
            'error_code' => $metadata['error'] ?? null,
            'metadata' => json_encode($metadata)
        ]);
    }
    
    /**
     * AJAX handler to flush batch
     */
    public function flush_batch_ajax() {
        check_ajax_referer('ufio_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'Permission denied']);
        }
        
        $this->flush_batch();
        wp_send_json_success(['message' => 'Batch flushed successfully']);
    }
    
    /**
     * Get batch queue status
     */
    public function get_queue_status() {
        return [
            'queue_size' => count($this->batch_queue),
            'batch_size_limit' => $this->batch_size,
            'buffer_timeout' => $this->buffer_timeout,
            'oldest_queued' => !empty($this->batch_queue) ? min(array_column($this->batch_queue, 'queued_at')) : null
        ];
    }
}
