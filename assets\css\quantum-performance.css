/**
 * 🚀 UFIO ENTERPRISE PRO - QUANTUM PERFORMANCE OPTIMIZATION
 * 
 * Revolutionary performance-optimized CSS with hardware acceleration
 * Built for lightning-fast enterprise WordPress plugins
 * 
 * @package UFIO_Enterprise_Pro
 * @version 10.0.0
 * <AUTHOR> Enterprise Performance Team
 */

/* ===== QUANTUM PERFORMANCE OPTIMIZATIONS ===== */

/* Hardware Acceleration for All Interactive Elements */
.ufio-btn,
.ufio-card,
.ufio-modal,
.ufio-notification,
.ufio-progress-bar {
  will-change: transform, opacity;
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* GPU-Accelerated Animations */
.ufio-quantum-accelerated {
  transform: translate3d(0, 0, 0);
  will-change: transform;
}

/* ===== ULTRA-FAST LOADING STATES ===== */
.ufio-loading-skeleton {
  background: linear-gradient(90deg, 
    var(--ufio-gray-200) 25%, 
    var(--ufio-gray-100) 50%, 
    var(--ufio-gray-200) 75%
  );
  background-size: 200% 100%;
  animation: ufio-skeleton-loading 1.5s infinite;
  border-radius: var(--ufio-radius-lg);
}

@keyframes ufio-skeleton-loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

.ufio-loading-skeleton-text {
  height: 1em;
  margin-bottom: var(--ufio-space-2);
}

.ufio-loading-skeleton-title {
  height: 1.5em;
  width: 60%;
  margin-bottom: var(--ufio-space-4);
}

.ufio-loading-skeleton-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.ufio-loading-skeleton-button {
  height: 40px;
  width: 120px;
  border-radius: var(--ufio-radius-lg);
}

/* ===== QUANTUM DASHBOARD LAYOUT ===== */
.ufio-dashboard {
  display: grid;
  grid-template-areas: 
    "header header header"
    "sidebar main aside"
    "footer footer footer";
  grid-template-columns: 280px 1fr 320px;
  grid-template-rows: auto 1fr auto;
  min-height: 100vh;
  gap: var(--ufio-space-6);
  padding: var(--ufio-space-6);
  background: var(--ufio-gray-50);
}

.ufio-dashboard-header {
  grid-area: header;
  background: white;
  border-radius: var(--ufio-radius-2xl);
  padding: var(--ufio-space-6);
  box-shadow: var(--ufio-shadow-base);
  border: 1px solid var(--ufio-gray-200);
}

.ufio-dashboard-sidebar {
  grid-area: sidebar;
  background: white;
  border-radius: var(--ufio-radius-2xl);
  padding: var(--ufio-space-6);
  box-shadow: var(--ufio-shadow-base);
  border: 1px solid var(--ufio-gray-200);
  height: fit-content;
  position: sticky;
  top: var(--ufio-space-6);
}

.ufio-dashboard-main {
  grid-area: main;
  display: flex;
  flex-direction: column;
  gap: var(--ufio-space-6);
}

.ufio-dashboard-aside {
  grid-area: aside;
  display: flex;
  flex-direction: column;
  gap: var(--ufio-space-6);
  height: fit-content;
  position: sticky;
  top: var(--ufio-space-6);
}

/* ===== ENTERPRISE HEADER SYSTEM ===== */
.ufio-enterprise-header {
  background: var(--ufio-gradient-enterprise);
  border-radius: var(--ufio-radius-2xl);
  padding: var(--ufio-space-8);
  margin-bottom: var(--ufio-space-8);
  position: relative;
  overflow: hidden;
}

.ufio-enterprise-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.ufio-enterprise-title {
  font-size: var(--ufio-text-4xl);
  font-weight: var(--ufio-font-weight-bold);
  color: var(--ufio-gray-900);
  margin: 0 0 var(--ufio-space-4) 0;
  position: relative;
  z-index: 1;
}

.ufio-enterprise-subtitle {
  font-size: var(--ufio-text-lg);
  color: var(--ufio-gray-700);
  margin: 0 0 var(--ufio-space-6) 0;
  position: relative;
  z-index: 1;
}

.ufio-enterprise-status {
  display: inline-flex;
  align-items: center;
  gap: var(--ufio-space-2);
  background: rgba(255, 255, 255, 0.9);
  padding: var(--ufio-space-3) var(--ufio-space-4);
  border-radius: var(--ufio-radius-full);
  font-size: var(--ufio-text-sm);
  font-weight: var(--ufio-font-weight-semibold);
  color: var(--ufio-success-700);
  position: relative;
  z-index: 1;
}

.ufio-enterprise-status::before {
  content: '';
  width: 8px;
  height: 8px;
  background: var(--ufio-success-500);
  border-radius: 50%;
  animation: ufio-pulse 2s infinite;
}

@keyframes ufio-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* ===== QUANTUM METRICS DISPLAY ===== */
.ufio-metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--ufio-space-6);
  margin-bottom: var(--ufio-space-8);
}

.ufio-metric-card {
  background: white;
  border-radius: var(--ufio-radius-xl);
  padding: var(--ufio-space-6);
  box-shadow: var(--ufio-shadow-base);
  border: 1px solid var(--ufio-gray-200);
  position: relative;
  overflow: hidden;
  transition: var(--ufio-transition-normal);
}

.ufio-metric-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--ufio-shadow-xl);
}

.ufio-metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--ufio-gradient-primary);
}

.ufio-metric-icon {
  width: 48px;
  height: 48px;
  background: var(--ufio-gradient-primary);
  border-radius: var(--ufio-radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--ufio-text-xl);
  color: white;
  margin-bottom: var(--ufio-space-4);
}

.ufio-metric-value {
  font-size: var(--ufio-text-3xl);
  font-weight: var(--ufio-font-weight-bold);
  color: var(--ufio-gray-900);
  margin-bottom: var(--ufio-space-2);
}

.ufio-metric-label {
  font-size: var(--ufio-text-sm);
  color: var(--ufio-gray-600);
  font-weight: var(--ufio-font-weight-medium);
  margin-bottom: var(--ufio-space-3);
}

.ufio-metric-change {
  display: inline-flex;
  align-items: center;
  gap: var(--ufio-space-1);
  font-size: var(--ufio-text-xs);
  font-weight: var(--ufio-font-weight-semibold);
  padding: var(--ufio-space-1) var(--ufio-space-2);
  border-radius: var(--ufio-radius-base);
}

.ufio-metric-change.positive {
  background: var(--ufio-success-50);
  color: var(--ufio-success-700);
}

.ufio-metric-change.negative {
  background: var(--ufio-error-50);
  color: var(--ufio-error-700);
}

/* ===== REAL-TIME ANALYTICS CHARTS ===== */
.ufio-chart-container {
  background: white;
  border-radius: var(--ufio-radius-xl);
  padding: var(--ufio-space-6);
  box-shadow: var(--ufio-shadow-base);
  border: 1px solid var(--ufio-gray-200);
  margin-bottom: var(--ufio-space-6);
}

.ufio-chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--ufio-space-6);
  padding-bottom: var(--ufio-space-4);
  border-bottom: 1px solid var(--ufio-gray-200);
}

.ufio-chart-title {
  font-size: var(--ufio-text-lg);
  font-weight: var(--ufio-font-weight-bold);
  color: var(--ufio-gray-900);
  margin: 0;
}

.ufio-chart-controls {
  display: flex;
  gap: var(--ufio-space-2);
}

.ufio-chart-body {
  height: 300px;
  position: relative;
}

/* ===== QUANTUM PERFORMANCE INDICATORS ===== */
.ufio-performance-indicator {
  display: inline-flex;
  align-items: center;
  gap: var(--ufio-space-2);
  padding: var(--ufio-space-2) var(--ufio-space-3);
  background: var(--ufio-gray-50);
  border-radius: var(--ufio-radius-full);
  font-size: var(--ufio-text-xs);
  font-weight: var(--ufio-font-weight-semibold);
}

.ufio-performance-indicator.excellent {
  background: var(--ufio-success-50);
  color: var(--ufio-success-700);
}

.ufio-performance-indicator.good {
  background: var(--ufio-primary-50);
  color: var(--ufio-primary-700);
}

.ufio-performance-indicator.warning {
  background: var(--ufio-warning-50);
  color: var(--ufio-warning-700);
}

.ufio-performance-indicator.critical {
  background: var(--ufio-error-50);
  color: var(--ufio-error-700);
}

.ufio-performance-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: currentColor;
  animation: ufio-pulse 2s infinite;
}

/* ===== ENTERPRISE TABLE SYSTEM ===== */
.ufio-table-container {
  background: white;
  border-radius: var(--ufio-radius-xl);
  box-shadow: var(--ufio-shadow-base);
  border: 1px solid var(--ufio-gray-200);
  overflow: hidden;
}

.ufio-table {
  width: 100%;
  border-collapse: collapse;
}

.ufio-table th {
  background: var(--ufio-gray-50);
  padding: var(--ufio-space-4) var(--ufio-space-6);
  text-align: left;
  font-size: var(--ufio-text-sm);
  font-weight: var(--ufio-font-weight-semibold);
  color: var(--ufio-gray-700);
  border-bottom: 1px solid var(--ufio-gray-200);
}

.ufio-table td {
  padding: var(--ufio-space-4) var(--ufio-space-6);
  border-bottom: 1px solid var(--ufio-gray-100);
  font-size: var(--ufio-text-sm);
  color: var(--ufio-gray-900);
}

.ufio-table tbody tr:hover {
  background: var(--ufio-gray-50);
}

.ufio-table tbody tr:last-child td {
  border-bottom: none;
}

/* ===== RESPONSIVE QUANTUM OPTIMIZATIONS ===== */
@media (max-width: 1200px) {
  .ufio-dashboard {
    grid-template-areas: 
      "header header"
      "main aside"
      "footer footer";
    grid-template-columns: 1fr 300px;
  }
  
  .ufio-dashboard-sidebar {
    display: none;
  }
}

@media (max-width: 768px) {
  .ufio-dashboard {
    grid-template-areas: 
      "header"
      "main"
      "aside"
      "footer";
    grid-template-columns: 1fr;
    padding: var(--ufio-space-4);
    gap: var(--ufio-space-4);
  }
  
  .ufio-dashboard-aside {
    position: static;
  }
  
  .ufio-metrics-grid {
    grid-template-columns: 1fr;
    gap: var(--ufio-space-4);
  }
  
  .ufio-enterprise-header {
    padding: var(--ufio-space-6);
  }
  
  .ufio-enterprise-title {
    font-size: var(--ufio-text-2xl);
  }
}

/* ===== QUANTUM ACCESSIBILITY ENHANCEMENTS ===== */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

@media (prefers-color-scheme: dark) {
  :root {
    --ufio-gray-50: #1e293b;
    --ufio-gray-100: #334155;
    --ufio-gray-200: #475569;
    --ufio-gray-900: #f8fafc;
  }
}

/* ===== PRINT OPTIMIZATIONS ===== */
@media print {
  .ufio-btn,
  .ufio-modal,
  .ufio-notification,
  .ufio-spinner {
    display: none !important;
  }
  
  .ufio-card {
    box-shadow: none !important;
    border: 1px solid #000 !important;
  }
}
