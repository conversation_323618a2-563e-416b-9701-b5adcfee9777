{"name": "ufio-command-center", "version": "1.0.0", "description": "Ultra Featured Image Optimizer Command Center SPA", "main": "src/index.js", "scripts": {"dev": "webpack serve --mode development", "build": "webpack --mode production", "build:analyze": "webpack-bundle-analyzer dist/static/js/*.js", "test": "jest", "lint": "eslint src/", "format": "prettier --write src/"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-window": "^1.8.8", "react-window-infinite-loader": "^1.0.9", "react-virtualized-auto-sizer": "^1.0.20", "recharts": "^2.8.0", "socket.io-client": "^4.7.4", "framer-motion": "^10.16.16", "react-hot-toast": "^2.4.1", "react-query": "^3.39.3", "zustand": "^4.4.7", "date-fns": "^2.30.0", "clsx": "^2.0.0", "react-intersection-observer": "^9.5.3", "react-use": "^17.4.0"}, "devDependencies": {"@babel/core": "^7.23.6", "@babel/preset-env": "^7.23.6", "@babel/preset-react": "^7.23.3", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "babel-loader": "^9.1.3", "css-loader": "^6.8.1", "html-webpack-plugin": "^5.6.0", "mini-css-extract-plugin": "^2.7.6", "postcss": "^8.4.32", "postcss-loader": "^7.3.3", "postcss-preset-env": "^9.3.0", "style-loader": "^3.3.3", "webpack": "^5.89.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1", "webpack-bundle-analyzer": "^4.10.1", "terser-webpack-plugin": "^5.3.9", "compression-webpack-plugin": "^10.0.0", "eslint": "^8.56.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^3.1.1", "jest": "^29.7.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.5"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "keywords": ["wordpress", "react", "spa", "image-optimization", "seo", "ai"], "author": "Elite WordPress Developer", "license": "GPL-2.0-or-later"}