import React, { createContext, useContext, useEffect, useState } from 'react';

const ThemeContext = createContext();

export function useTheme() {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within ThemeProvider');
  }
  return context;
}

export function ThemeProvider({ children }) {
  const [theme, setTheme] = useState('light');
  const [systemPreference, setSystemPreference] = useState('light');
  const [autoTheme, setAutoTheme] = useState(true);
  
  // Detect system theme preference
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    const handleChange = (e) => {
      const newPreference = e.matches ? 'dark' : 'light';
      setSystemPreference(newPreference);
      
      if (autoTheme) {
        setTheme(newPreference);
      }
    };
    
    // Set initial preference
    setSystemPreference(mediaQuery.matches ? 'dark' : 'light');
    
    // Listen for changes
    mediaQuery.addEventListener('change', handleChange);
    
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [autoTheme]);
  
  // Load saved theme preference
  useEffect(() => {
    const savedTheme = localStorage.getItem('ufio-theme');
    const savedAutoTheme = localStorage.getItem('ufio-auto-theme');
    
    if (savedAutoTheme !== null) {
      const isAuto = savedAutoTheme === 'true';
      setAutoTheme(isAuto);
      
      if (isAuto) {
        setTheme(systemPreference);
      } else if (savedTheme) {
        setTheme(savedTheme);
      }
    } else {
      // Default to auto theme
      setTheme(systemPreference);
    }
  }, [systemPreference]);
  
  // Apply theme to document
  useEffect(() => {
    document.documentElement.setAttribute('data-theme', theme);
    document.documentElement.className = `theme-${theme}`;
    
    // Update meta theme-color for mobile browsers
    const metaThemeColor = document.querySelector('meta[name="theme-color"]');
    if (metaThemeColor) {
      metaThemeColor.setAttribute('content', 
        theme === 'dark' ? '#1a1a1a' : '#ffffff'
      );
    }
    
    // Save preference
    if (!autoTheme) {
      localStorage.setItem('ufio-theme', theme);
    }
  }, [theme, autoTheme]);
  
  // Toggle between light and dark
  const toggleTheme = () => {
    if (autoTheme) {
      // Switch to manual mode and toggle
      setAutoTheme(false);
      const newTheme = theme === 'light' ? 'dark' : 'light';
      setTheme(newTheme);
      localStorage.setItem('ufio-auto-theme', 'false');
      localStorage.setItem('ufio-theme', newTheme);
    } else {
      // Toggle current theme
      const newTheme = theme === 'light' ? 'dark' : 'light';
      setTheme(newTheme);
      localStorage.setItem('ufio-theme', newTheme);
    }
  };
  
  // Set specific theme
  const setSpecificTheme = (newTheme) => {
    setAutoTheme(false);
    setTheme(newTheme);
    localStorage.setItem('ufio-auto-theme', 'false');
    localStorage.setItem('ufio-theme', newTheme);
  };
  
  // Enable auto theme
  const enableAutoTheme = () => {
    setAutoTheme(true);
    setTheme(systemPreference);
    localStorage.setItem('ufio-auto-theme', 'true');
    localStorage.removeItem('ufio-theme');
  };
  
  // Get theme info
  const getThemeInfo = () => {
    return {
      current: theme,
      system: systemPreference,
      auto: autoTheme,
      isDark: theme === 'dark',
      isLight: theme === 'light'
    };
  };
  
  const value = {
    theme,
    systemPreference,
    autoTheme,
    toggleTheme,
    setTheme: setSpecificTheme,
    enableAutoTheme,
    getThemeInfo
  };
  
  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
}
