/**
 * Ultra Featured Image Optimizer Pro - Professional Admin JavaScript
 * Real-time functionality with modern UX
 */

(function($) {
    'use strict';

    // Main UFIO Admin object
    window.UFIOPro = {
        
        // Initialize all functionality
        init: function() {
            this.bindEvents();
            this.initComponents();
            this.startPeriodicUpdates();
        },

        // Bind all event handlers
        bindEvents: function() {
            // Bulk processing
            $(document).on('click', '#ufio-start-bulk', this.startBulkProcessing.bind(this));
            
            // Single post processing
            $(document).on('click', '#ufio-process-single', this.processSinglePost.bind(this));
            
            // API testing
            $(document).on('click', '#ufio-test-api-btn', this.testAPIConnection.bind(this));
            
            // Cache management
            $(document).on('click', '#ufio-clear-cache-btn', this.clearCache.bind(this));
            
            // Stats refresh
            $(document).on('click', '#ufio-refresh-stats', this.refreshStats.bind(this));
            
            // Range input updates
            $(document).on('input', '.ufio-range', this.updateRangeValue.bind(this));
        },

        // Initialize components
        initComponents: function() {
            this.initProgressBars();
            this.initRangeInputs();
            this.showWelcomeMessage();
        },

        // Start periodic updates
        startPeriodicUpdates: function() {
            // Refresh stats every 30 seconds
            setInterval(() => {
                this.refreshStats(true);
            }, 30000);
        },

        // REAL BULK PROCESSING FUNCTIONALITY
        startBulkProcessing: function(e) {
            e.preventDefault();
            
            const $button = $(e.currentTarget);
            const postType = $('#ufio-post-type').val();
            const batchSize = $('#ufio-batch-size').val();
            
            if (!confirm(ufioAjax.strings.confirm_bulk)) {
                return;
            }
            
            // Disable button and show loading
            $button.prop('disabled', true).html('<span class="ufio-spinner"></span> ' + ufioAjax.strings.processing);
            
            // Show progress container
            const $progressContainer = $('#ufio-bulk-progress');
            const $resultsContainer = $('#ufio-bulk-results');
            
            $progressContainer.show();
            $resultsContainer.hide();
            
            // Reset progress
            this.updateProgress(0, ufioAjax.strings.processing);
            
            // Start processing
            this.processBatch(postType, batchSize, $button);
        },

        // Process batch with real AJAX
        processBatch: function(postType, batchSize, $button) {
            $.ajax({
                url: ufioAjax.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'ufio_bulk_process',
                    nonce: ufioAjax.nonce,
                    post_type: postType,
                    batch_size: batchSize
                },
                success: (response) => {
                    if (response.success) {
                        this.updateProgress(100, ufioAjax.strings.completed);
                        this.showResults(response.data);
                        this.showNotification('success', response.data.message);
                        this.refreshStats();
                    } else {
                        this.showNotification('error', response.data.message || 'Processing failed');
                    }
                },
                error: (xhr, status, error) => {
                    this.showNotification('error', 'Network error: ' + error);
                },
                complete: () => {
                    $button.prop('disabled', false).html('<span class="dashicons dashicons-images-alt2"></span> Start Processing');
                }
            });
        },

        // Process single post
        processSinglePost: function(e) {
            e.preventDefault();
            
            const $button = $(e.currentTarget);
            const postId = $('#ufio-single-post-id').val();
            const $result = $('#ufio-single-result');
            
            if (!postId || postId < 1) {
                this.showNotification('error', 'Please enter a valid post ID');
                return;
            }
            
            $button.prop('disabled', true).html('<span class="ufio-spinner"></span> Processing...');
            $result.hide();
            
            $.ajax({
                url: ufioAjax.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'ufio_process_single',
                    nonce: ufioAjax.nonce,
                    post_id: postId
                },
                success: (response) => {
                    if (response.success) {
                        $result.removeClass('error').addClass('success').text(response.data.message).show();
                        this.showNotification('success', response.data.message);
                        this.refreshStats();
                    } else {
                        $result.removeClass('success').addClass('error').text(response.data.message).show();
                        this.showNotification('error', response.data.message);
                    }
                },
                error: (xhr, status, error) => {
                    $result.removeClass('success').addClass('error').text('Network error: ' + error).show();
                },
                complete: () => {
                    $button.prop('disabled', false).html('<span class="dashicons dashicons-admin-media"></span> Process Single Post');
                }
            });
        },

        // Test API connection
        testAPIConnection: function(e) {
            e.preventDefault();
            
            const $button = $(e.currentTarget);
            const $result = $('#ufio-api-test-result');
            
            $button.prop('disabled', true).text('Testing...');
            $result.empty();
            
            $.ajax({
                url: ufioAjax.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'ufio_test_api',
                    nonce: ufioAjax.nonce
                },
                success: (response) => {
                    if (response.success) {
                        $result.html('<div class="notice notice-success inline"><p>' + response.data.message + '</p></div>');
                        this.showNotification('success', response.data.message);
                    } else {
                        $result.html('<div class="notice notice-error inline"><p>' + response.data.message + '</p></div>');
                        this.showNotification('error', response.data.message);
                    }
                },
                error: (xhr, status, error) => {
                    $result.html('<div class="notice notice-error inline"><p>Connection failed: ' + error + '</p></div>');
                },
                complete: () => {
                    $button.prop('disabled', false).text('Test API Connection');
                }
            });
        },

        // Clear cache
        clearCache: function(e) {
            e.preventDefault();
            
            if (!confirm(ufioAjax.strings.confirm_clear)) {
                return;
            }
            
            const $button = $(e.currentTarget);
            const $result = $('#ufio-cache-result');
            
            $button.prop('disabled', true).html('<span class="ufio-spinner"></span> Clearing...');
            $result.empty();
            
            $.ajax({
                url: ufioAjax.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'ufio_clear_cache',
                    nonce: ufioAjax.nonce
                },
                success: (response) => {
                    if (response.success) {
                        $result.addClass('success').text(response.data.message);
                        this.showNotification('success', response.data.message);
                        this.refreshStats();
                    } else {
                        $result.removeClass('success').text(response.data.message);
                        this.showNotification('error', response.data.message);
                    }
                },
                error: (xhr, status, error) => {
                    $result.removeClass('success').text('Error: ' + error);
                },
                complete: () => {
                    $button.prop('disabled', false).html('<span class="dashicons dashicons-trash"></span> Clear All Cache');
                }
            });
        },

        // Refresh statistics
        refreshStats: function(silent = false) {
            if (!silent) {
                this.showNotification('info', 'Refreshing statistics...');
            }
            
            $.ajax({
                url: ufioAjax.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'ufio_get_stats',
                    nonce: ufioAjax.nonce
                },
                success: (response) => {
                    if (response.success) {
                        this.updateStatsDisplay(response.data);
                        if (!silent) {
                            this.showNotification('success', 'Statistics updated');
                        }
                    }
                },
                error: (xhr, status, error) => {
                    if (!silent) {
                        this.showNotification('error', 'Failed to refresh stats');
                    }
                }
            });
        },

        // Update progress bar
        updateProgress: function(percentage, status) {
            const $progressFill = $('#ufio-bulk-progress .ufio-progress-fill');
            const $progressText = $('#ufio-bulk-progress .ufio-progress-text');
            const $progressStatus = $('#ufio-progress-status');
            
            $progressFill.css('width', percentage + '%');
            $progressText.text(percentage + '%');
            $progressStatus.text(status);
        },

        // Show processing results
        showResults: function(data) {
            const $resultsContainer = $('#ufio-bulk-results');
            const $resultsContent = $('#ufio-results-content');
            
            let html = '<div class="ufio-results-summary">';
            html += '<p><strong>Processing completed successfully!</strong></p>';
            html += '<p>Processed: ' + data.processed + ' posts</p>';
            html += '<p>Total: ' + data.total + ' posts</p>';
            
            if (data.errors && data.errors.length > 0) {
                html += '<p>Errors: ' + data.errors.length + '</p>';
                html += '<details><summary>View Errors</summary><ul>';
                data.errors.forEach(error => {
                    html += '<li>' + error + '</li>';
                });
                html += '</ul></details>';
            }
            
            html += '</div>';
            
            $resultsContent.html(html);
            $resultsContainer.show();
        },

        // Update stats display
        updateStatsDisplay: function(stats) {
            // Update stat cards
            $('.ufio-stat-number').each(function() {
                const $this = $(this);
                const $card = $this.closest('.ufio-stat-card');
                
                if ($card.length) {
                    const currentValue = parseInt($this.text()) || 0;
                    let newValue = currentValue;
                    
                    // Determine which stat this is based on card position or class
                    const cardIndex = $card.index();
                    switch (cardIndex) {
                        case 0:
                            newValue = stats.total_posts;
                            break;
                        case 1:
                            newValue = stats.posts_with_featured;
                            break;
                        case 2:
                            newValue = stats.posts_without_featured;
                            break;
                        case 3:
                            newValue = stats.coverage_percentage;
                            break;
                    }
                    
                    // Animate number change
                    this.animateNumber(currentValue, newValue, $this);
                }
            });
            
            // Update progress bars
            $('.ufio-progress-fill').each(function() {
                const $this = $(this);
                const $text = $this.siblings('.ufio-progress-text');
                
                if ($this.closest('.ufio-progress-section').length) {
                    $this.css('width', stats.coverage_percentage + '%');
                    $text.text(stats.coverage_percentage + '%');
                }
            });
        },

        // Animate number changes
        animateNumber: function(from, to, $element) {
            const duration = 1000;
            const steps = 30;
            const stepValue = (to - from) / steps;
            let current = from;
            let step = 0;
            
            const timer = setInterval(() => {
                step++;
                current += stepValue;
                
                if (step >= steps) {
                    current = to;
                    clearInterval(timer);
                }
                
                $element.text(Math.round(current));
            }, duration / steps);
        },

        // Initialize progress bars with animation
        initProgressBars: function() {
            $('.ufio-progress-fill').each(function() {
                const $this = $(this);
                const width = $this.css('width');
                
                $this.css('width', '0%');
                
                setTimeout(() => {
                    $this.css('width', width);
                }, 100);
            });
        },

        // Initialize range inputs
        initRangeInputs: function() {
            $('.ufio-range').each(function() {
                const $this = $(this);
                const $value = $this.siblings('.ufio-range-value');
                $value.text($this.val() + '%');
            });
        },

        // Update range input value display
        updateRangeValue: function(e) {
            const $input = $(e.currentTarget);
            const $value = $input.siblings('.ufio-range-value');
            $value.text($input.val() + '%');
        },

        // Show welcome message
        showWelcomeMessage: function() {
            if ($('.ufio-dashboard').length && !sessionStorage.getItem('ufio_welcome_shown')) {
                setTimeout(() => {
                    this.showNotification('success', 'Welcome to Ultra Featured Image Optimizer Pro! Your plugin is ready to use.', 5000);
                    sessionStorage.setItem('ufio_welcome_shown', 'true');
                }, 1000);
            }
        },

        // Show notification
        showNotification: function(type, message, duration = 3000) {
            const $notification = $('<div class="ufio-notification ufio-notification-' + type + '">' + message + '</div>');
            
            // Add to page
            if (!$('#ufio-notifications').length) {
                $('body').append('<div id="ufio-notifications"></div>');
            }
            
            $('#ufio-notifications').append($notification);
            
            // Animate in
            setTimeout(() => {
                $notification.addClass('show');
            }, 10);
            
            // Auto remove
            setTimeout(() => {
                $notification.removeClass('show');
                setTimeout(() => {
                    $notification.remove();
                }, 300);
            }, duration);
        },

        // Utility functions
        formatNumber: function(num) {
            return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        },

        formatBytes: function(bytes, decimals = 2) {
            if (bytes === 0) return '0 Bytes';
            
            const k = 1024;
            const dm = decimals < 0 ? 0 : decimals;
            const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
            
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            
            return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        UFIOPro.init();
    });

})(jQuery);

// Add notification styles
const notificationStyles = `
<style>
#ufio-notifications {
    position: fixed;
    top: 32px;
    right: 20px;
    z-index: 999999;
    pointer-events: none;
}

.ufio-notification {
    background: #fff;
    border-left: 4px solid #2271b1;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 12px 16px;
    margin-bottom: 8px;
    border-radius: 4px;
    max-width: 350px;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    pointer-events: auto;
    font-size: 14px;
    line-height: 1.4;
}

.ufio-notification.show {
    transform: translateX(0);
}

.ufio-notification-success {
    border-left-color: #00a32a;
}

.ufio-notification-error {
    border-left-color: #d63638;
}

.ufio-notification-warning {
    border-left-color: #dba617;
}

.ufio-notification-info {
    border-left-color: #72aee6;
}
</style>
`;

document.head.insertAdjacentHTML('beforeend', notificationStyles);
