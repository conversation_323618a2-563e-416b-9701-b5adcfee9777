/**
 * UFIO WebSocket Server
 * Real-time updates for Command Center
 */

const http = require('http');
const { Server } = require('socket.io');
const Redis = require('redis');

// Configuration
const PORT = process.env.PORT || 8080;
const REDIS_URL = process.env.REDIS_URL || 'redis://localhost:6379';
const CORS_ORIGIN = process.env.CORS_ORIGIN || 'http://localhost:3000';

// Create HTTP server
const server = http.createServer();

// Create Socket.IO server with CORS
const io = new Server(server, {
  cors: {
    origin: [CORS_ORIGIN, /\.local$/, /localhost/],
    methods: ['GET', 'POST'],
    credentials: true
  },
  transports: ['websocket', 'polling']
});

// Redis client for pub/sub
let redisClient;
let redisSubscriber;

async function initRedis() {
  try {
    redisClient = Redis.createClient({ url: REDIS_URL });
    redisSubscriber = redisClient.duplicate();
    
    await redisClient.connect();
    await redisSubscriber.connect();
    
    console.log('✅ Redis connected');
    
    // Subscribe to UFIO channels
    await redisSubscriber.subscribe('ufio:processing', (message) => {
      const data = JSON.parse(message);
      io.emit('processing_update', data);
    });
    
    await redisSubscriber.subscribe('ufio:system', (message) => {
      const data = JSON.parse(message);
      io.emit('system_alert', data);
    });
    
    await redisSubscriber.subscribe('ufio:stats', (message) => {
      const data = JSON.parse(message);
      io.emit('stats_update', data);
    });
    
    await redisSubscriber.subscribe('ufio:queue', (message) => {
      const data = JSON.parse(message);
      io.emit('queue_update', data);
    });
    
  } catch (error) {
    console.error('❌ Redis connection failed:', error);
    // Continue without Redis - fallback to memory
  }
}

// Connection tracking
const connections = new Map();
const rooms = new Map();

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log(`🔌 Client connected: ${socket.id}`);
  
  // Store connection info
  connections.set(socket.id, {
    connectedAt: Date.now(),
    userId: null,
    channels: new Set()
  });
  
  // Authentication
  socket.on('authenticate', async (data) => {
    try {
      const { token, userId } = data;
      
      // Verify WordPress nonce/token here
      // For now, accept all connections
      
      const connection = connections.get(socket.id);
      if (connection) {
        connection.userId = userId;
        connection.authenticated = true;
      }
      
      socket.emit('authenticated', { success: true });
      console.log(`✅ Client authenticated: ${socket.id} (User: ${userId})`);
      
    } catch (error) {
      socket.emit('authentication_error', { error: error.message });
      console.error(`❌ Authentication failed for ${socket.id}:`, error);
    }
  });
  
  // Channel subscription
  socket.on('subscribe', (data) => {
    const { channels } = data;
    const connection = connections.get(socket.id);
    
    if (!connection || !connection.authenticated) {
      socket.emit('error', { message: 'Not authenticated' });
      return;
    }
    
    channels.forEach(channel => {
      socket.join(channel);
      connection.channels.add(channel);
      
      // Add to room tracking
      if (!rooms.has(channel)) {
        rooms.set(channel, new Set());
      }
      rooms.get(channel).add(socket.id);
    });
    
    socket.emit('subscribed', { channels });
    console.log(`📡 Client ${socket.id} subscribed to:`, channels);
  });
  
  // Channel unsubscription
  socket.on('unsubscribe', (data) => {
    const { channels } = data;
    const connection = connections.get(socket.id);
    
    if (!connection) return;
    
    channels.forEach(channel => {
      socket.leave(channel);
      connection.channels.delete(channel);
      
      // Remove from room tracking
      if (rooms.has(channel)) {
        rooms.get(channel).delete(socket.id);
        if (rooms.get(channel).size === 0) {
          rooms.delete(channel);
        }
      }
    });
    
    socket.emit('unsubscribed', { channels });
  });
  
  // Handle messages from client
  socket.on('message', async (data) => {
    const connection = connections.get(socket.id);
    
    if (!connection || !connection.authenticated) {
      socket.emit('error', { message: 'Not authenticated' });
      return;
    }
    
    try {
      await handleClientMessage(socket, data);
    } catch (error) {
      socket.emit('error', { message: error.message });
      console.error(`❌ Message handling error for ${socket.id}:`, error);
    }
  });
  
  // Ping/pong for connection health
  socket.on('ping', () => {
    socket.emit('pong', { timestamp: Date.now() });
  });
  
  // Disconnect handling
  socket.on('disconnect', (reason) => {
    console.log(`🔌 Client disconnected: ${socket.id} (${reason})`);
    
    const connection = connections.get(socket.id);
    if (connection) {
      // Clean up room subscriptions
      connection.channels.forEach(channel => {
        if (rooms.has(channel)) {
          rooms.get(channel).delete(socket.id);
          if (rooms.get(channel).size === 0) {
            rooms.delete(channel);
          }
        }
      });
    }
    
    connections.delete(socket.id);
  });
});

// Handle client messages
async function handleClientMessage(socket, data) {
  const { type, payload } = data;
  
  switch (type) {
    case 'bulk_assign_featured':
      // Trigger bulk processing
      await triggerBulkProcessing(payload.postIds);
      socket.emit('bulk_processing_started', { 
        postIds: payload.postIds,
        message: `Started processing ${payload.postIds.length} posts`
      });
      break;
      
    case 'bulk_add_to_queue':
      // Add posts to queue
      await addPostsToQueue(payload.postIds, payload.priority);
      socket.emit('posts_queued', {
        postIds: payload.postIds,
        priority: payload.priority,
        message: `Added ${payload.postIds.length} posts to queue`
      });
      break;
      
    case 'get_stats':
      // Send current stats
      const stats = await getCurrentStats();
      socket.emit('stats_update', stats);
      break;
      
    case 'test_microservice':
      // Test microservice connection
      const status = await testMicroservice();
      socket.emit('microservice_status', status);
      break;
      
    default:
      console.log(`Unknown message type: ${type}`);
  }
}

// Utility functions
async function triggerBulkProcessing(postIds) {
  // This would integrate with WordPress to trigger processing
  console.log(`🔄 Triggering bulk processing for ${postIds.length} posts`);
  
  // Simulate processing updates
  setTimeout(() => {
    io.emit('processing_update', {
      type: 'batch_started',
      postIds,
      timestamp: Date.now()
    });
  }, 1000);
  
  setTimeout(() => {
    io.emit('processing_update', {
      type: 'batch_completed',
      processed: postIds.length,
      success: Math.floor(postIds.length * 0.9),
      failed: Math.floor(postIds.length * 0.1),
      timestamp: Date.now()
    });
  }, 5000);
}

async function addPostsToQueue(postIds, priority) {
  console.log(`📋 Adding ${postIds.length} posts to queue with priority ${priority}`);
  
  // Emit queue update
  io.emit('queue_update', {
    type: 'posts_added',
    count: postIds.length,
    priority,
    timestamp: Date.now()
  });
}

async function getCurrentStats() {
  // This would fetch real stats from WordPress/Redis
  return {
    total_posts: Math.floor(Math.random() * 10000) + 5000,
    processed_today: Math.floor(Math.random() * 500) + 100,
    queue_size: Math.floor(Math.random() * 100) + 10,
    success_rate: Math.floor(Math.random() * 10) + 90,
    timestamp: Date.now()
  };
}

async function testMicroservice() {
  // Simulate microservice test
  const isHealthy = Math.random() > 0.1; // 90% success rate
  
  return {
    status: isHealthy ? 'healthy' : 'error',
    response_time: Math.floor(Math.random() * 100) + 50,
    message: isHealthy ? 'Service responding normally' : 'Connection timeout',
    timestamp: Date.now()
  };
}

// Health check endpoint
server.on('request', (req, res) => {
  if (req.url === '/health') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      status: 'healthy',
      connections: connections.size,
      rooms: rooms.size,
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      timestamp: Date.now()
    }));
  } else {
    res.writeHead(404);
    res.end('Not Found');
  }
});

// Periodic stats broadcast
setInterval(async () => {
  if (connections.size > 0) {
    const stats = await getCurrentStats();
    io.emit('stats_update', stats);
  }
}, 30000); // Every 30 seconds

// Graceful shutdown
process.on('SIGTERM', async () => {
  console.log('🛑 Shutting down gracefully...');
  
  io.emit('server_shutdown', { message: 'Server is shutting down' });
  
  if (redisClient) {
    await redisClient.quit();
  }
  if (redisSubscriber) {
    await redisSubscriber.quit();
  }
  
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});

// Start server
async function start() {
  await initRedis();
  
  server.listen(PORT, () => {
    console.log(`🚀 UFIO WebSocket Server running on port ${PORT}`);
    console.log(`📡 Accepting connections from: ${CORS_ORIGIN}`);
  });
}

start().catch(console.error);
