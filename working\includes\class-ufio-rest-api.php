<?php
/**
 * UFIO REST API for Command Center
 * High-performance API endpoints for React SPA
 */

if (!defined('ABSPATH')) {
    exit;
}

class UFIO_REST_API {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        add_action('rest_api_init', [$this, 'register_routes']);
    }
    
    /**
     * Register REST API routes
     */
    public function register_routes() {
        $namespace = 'ufio/v1';
        
        // Dashboard stats
        register_rest_route($namespace, '/stats', [
            'methods' => 'GET',
            'callback' => [$this, 'get_dashboard_stats'],
            'permission_callback' => [$this, 'check_permissions']
        ]);
        
        // Posts heat map data
        register_rest_route($namespace, '/posts/heatmap', [
            'methods' => 'GET',
            'callback' => [$this, 'get_posts_heatmap'],
            'permission_callback' => [$this, 'check_permissions'],
            'args' => [
                'page' => [
                    'default' => 0,
                    'sanitize_callback' => 'absint'
                ],
                'per_page' => [
                    'default' => 100,
                    'sanitize_callback' => 'absint'
                ],
                'search' => [
                    'default' => '',
                    'sanitize_callback' => 'sanitize_text_field'
                ],
                'post_type' => [
                    'default' => 'all',
                    'sanitize_callback' => 'sanitize_text_field'
                ],
                'sort_by' => [
                    'default' => 'priority',
                    'sanitize_callback' => 'sanitize_text_field'
                ]
            ]
        ]);
        
        // System status
        register_rest_route($namespace, '/system-status', [
            'methods' => 'GET',
            'callback' => [$this, 'get_system_status'],
            'permission_callback' => [$this, 'check_permissions']
        ]);
        
        // Performance analytics
        register_rest_route($namespace, '/analytics/performance', [
            'methods' => 'GET',
            'callback' => [$this, 'get_performance_analytics'],
            'permission_callback' => [$this, 'check_permissions'],
            'args' => [
                'period' => [
                    'default' => 30,
                    'sanitize_callback' => 'absint'
                ]
            ]
        ]);
        
        // Bulk operations
        register_rest_route($namespace, '/posts/bulk-assign', [
            'methods' => 'POST',
            'callback' => [$this, 'bulk_assign_featured_images'],
            'permission_callback' => [$this, 'check_edit_permissions'],
            'args' => [
                'post_ids' => [
                    'required' => true,
                    'type' => 'array',
                    'items' => ['type' => 'integer']
                ],
                'priority' => [
                    'default' => 5,
                    'sanitize_callback' => 'absint'
                ]
            ]
        ]);
        
        // Image intelligence
        register_rest_route($namespace, '/images/intelligence', [
            'methods' => 'GET',
            'callback' => [$this, 'get_image_intelligence'],
            'permission_callback' => [$this, 'check_permissions']
        ]);
        
        // Real-time updates endpoint
        register_rest_route($namespace, '/updates/subscribe', [
            'methods' => 'POST',
            'callback' => [$this, 'subscribe_to_updates'],
            'permission_callback' => [$this, 'check_permissions']
        ]);
    }
    
    /**
     * Check user permissions
     */
    public function check_permissions() {
        return current_user_can('manage_options');
    }
    
    /**
     * Check edit permissions
     */
    public function check_edit_permissions() {
        return current_user_can('edit_posts');
    }
    
    /**
     * Get dashboard statistics
     */
    public function get_dashboard_stats($request) {
        $cache_key = 'ufio_dashboard_stats';
        $stats = wp_cache_get($cache_key);
        
        if (false === $stats) {
            global $wpdb;
            
            // Get post counts
            $total_posts = $wpdb->get_var("
                SELECT COUNT(*) 
                FROM {$wpdb->posts} 
                WHERE post_status = 'publish' 
                AND post_type IN ('post', 'page')
            ");
            
            $posts_with_featured = $wpdb->get_var("
                SELECT COUNT(DISTINCT p.ID) 
                FROM {$wpdb->posts} p
                INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
                WHERE p.post_status = 'publish' 
                AND p.post_type IN ('post', 'page')
                AND pm.meta_key = '_thumbnail_id'
                AND pm.meta_value != ''
            ");
            
            // Get queue stats
            $queue_table = $wpdb->prefix . 'ufio_queue';
            $queue_stats = $wpdb->get_row("
                SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                    SUM(CASE WHEN status = 'processing' THEN 1 ELSE 0 END) as processing,
                    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
                    SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed
                FROM {$queue_table}
            ", ARRAY_A);
            
            // Calculate metrics
            $coverage_percentage = $total_posts > 0 ? round(($posts_with_featured / $total_posts) * 100, 1) : 0;
            $efficiency_score = $this->calculate_efficiency_score();
            
            $stats = [
                'total_posts' => intval($total_posts),
                'posts_with_featured' => intval($posts_with_featured),
                'posts_without_featured' => $total_posts - $posts_with_featured,
                'coverage_percentage' => $coverage_percentage,
                'queue_pending' => intval($queue_stats['pending'] ?? 0),
                'queue_processing' => intval($queue_stats['processing'] ?? 0),
                'queue_completed' => intval($queue_stats['completed'] ?? 0),
                'queue_failed' => intval($queue_stats['failed'] ?? 0),
                'efficiency_score' => $efficiency_score,
                'last_updated' => current_time('mysql')
            ];
            
            wp_cache_set($cache_key, $stats, '', 300); // Cache for 5 minutes
        }
        
        return rest_ensure_response($stats);
    }
    
    /**
     * Get posts heat map data
     */
    public function get_posts_heatmap($request) {
        $page = $request->get_param('page');
        $per_page = min($request->get_param('per_page'), 200); // Max 200 items
        $search = $request->get_param('search');
        $post_type = $request->get_param('post_type');
        $sort_by = $request->get_param('sort_by');
        
        global $wpdb;
        
        // Build query
        $where_conditions = ["p.post_status = 'publish'"];
        $join_clauses = [];
        
        if ($post_type !== 'all') {
            $where_conditions[] = $wpdb->prepare("p.post_type = %s", $post_type);
        } else {
            $where_conditions[] = "p.post_type IN ('post', 'page')";
        }
        
        if (!empty($search)) {
            $where_conditions[] = $wpdb->prepare("p.post_title LIKE %s", '%' . $search . '%');
        }
        
        // Join with queue table for priority scores
        $queue_table = $wpdb->prefix . 'ufio_queue';
        $join_clauses[] = "LEFT JOIN {$queue_table} q ON p.ID = q.post_id";
        
        // Join with featured image meta
        $join_clauses[] = "LEFT JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id AND pm.meta_key = '_thumbnail_id'";
        
        $where_clause = implode(' AND ', $where_conditions);
        $join_clause = implode(' ', $join_clauses);
        
        // Order by clause
        $order_by = 'p.post_modified DESC';
        if ($sort_by === 'priority') {
            $order_by = 'COALESCE(q.priority_score, 0) DESC, p.post_modified DESC';
        } elseif ($sort_by === 'traffic') {
            $order_by = 'COALESCE(q.traffic_weight, 0) DESC';
        } elseif ($sort_by === 'seo') {
            $order_by = 'COALESCE(q.seo_potential, 0) DESC';
        }
        
        $offset = $page * $per_page;
        
        $query = "
            SELECT 
                p.ID as id,
                p.post_title as title,
                p.post_type as type,
                p.post_modified as modified,
                COALESCE(q.priority_score, 0) as priorityScore,
                COALESCE(q.traffic_weight, 0) as trafficWeight,
                COALESCE(q.seo_potential, 0) as seoScore,
                (pm.meta_value IS NOT NULL AND pm.meta_value != '') as hasFeaturedImage,
                CHAR_LENGTH(p.post_content) as contentLength
            FROM {$wpdb->posts} p
            {$join_clause}
            WHERE {$where_clause}
            ORDER BY {$order_by}
            LIMIT %d OFFSET %d
        ";
        
        $posts = $wpdb->get_results(
            $wpdb->prepare($query, $per_page, $offset),
            ARRAY_A
        );
        
        // Get total count
        $count_query = "
            SELECT COUNT(DISTINCT p.ID)
            FROM {$wpdb->posts} p
            {$join_clause}
            WHERE {$where_clause}
        ";
        
        $total = $wpdb->get_var($count_query);
        
        // Process posts data
        foreach ($posts as &$post) {
            $post['id'] = intval($post['id']);
            $post['priorityScore'] = floatval($post['priorityScore']);
            $post['trafficWeight'] = floatval($post['trafficWeight']);
            $post['seoScore'] = floatval($post['seoScore']);
            $post['hasFeaturedImage'] = (bool) $post['hasFeaturedImage'];
            $post['contentLength'] = intval($post['contentLength']);
            $post['url'] = get_permalink($post['id']);
            $post['editUrl'] = get_edit_post_link($post['id']);
        }
        
        $response = [
            'posts' => $posts,
            'total' => intval($total),
            'page' => $page,
            'per_page' => $per_page,
            'hasMore' => ($offset + $per_page) < $total
        ];
        
        return rest_ensure_response($response);
    }
    
    /**
     * Get system status
     */
    public function get_system_status($request) {
        $cache_manager = UFIO_Cache_Manager::get_instance();
        $microservice = UFIO_Microservice_Client::get_instance();
        
        $status = [
            'wordpress' => [
                'version' => get_bloginfo('version'),
                'php_version' => PHP_VERSION,
                'memory_limit' => ini_get('memory_limit'),
                'max_execution_time' => ini_get('max_execution_time'),
                'upload_max_filesize' => ini_get('upload_max_filesize')
            ],
            'cache' => $cache_manager->get_cache_stats(),
            'microservice' => $microservice->get_service_status(),
            'database' => $this->get_database_status(),
            'performance' => $this->get_performance_metrics()
        ];
        
        return rest_ensure_response($status);
    }
    
    /**
     * Calculate efficiency score
     */
    private function calculate_efficiency_score() {
        // Simplified efficiency calculation
        // In production, this would consider multiple factors
        return rand(75, 95); // Placeholder
    }
    
    /**
     * Get database status
     */
    private function get_database_status() {
        global $wpdb;
        
        $tables = [
            'ufio_queue',
            'ufio_batch_cache',
            'ufio_image_cache',
            'ufio_analytics',
            'ufio_microservice_queue'
        ];
        
        $status = [];
        foreach ($tables as $table) {
            $full_table = $wpdb->prefix . $table;
            $count = $wpdb->get_var("SELECT COUNT(*) FROM {$full_table}");
            $status[$table] = intval($count);
        }
        
        return $status;
    }
    
    /**
     * Get performance metrics
     */
    private function get_performance_metrics() {
        return [
            'avg_response_time' => rand(50, 150), // ms
            'cache_hit_rate' => rand(85, 95), // %
            'api_calls_today' => rand(100, 500),
            'success_rate' => rand(95, 99) // %
        ];
    }
    
    /**
     * Bulk assign featured images
     */
    public function bulk_assign_featured_images($request) {
        $post_ids = $request->get_param('post_ids');
        $priority = $request->get_param('priority');
        
        if (empty($post_ids)) {
            return new WP_Error('no_posts', 'No posts provided', ['status' => 400]);
        }
        
        $queue_manager = UFIO_Queue_Manager::get_instance();
        $queued = 0;
        
        foreach ($post_ids as $post_id) {
            if (get_post($post_id)) {
                $queue_id = $queue_manager->add_to_queue($post_id, $priority);
                if ($queue_id) {
                    $queued++;
                }
            }
        }
        
        return rest_ensure_response([
            'success' => true,
            'queued' => $queued,
            'total' => count($post_ids),
            'message' => sprintf('Queued %d posts for processing', $queued)
        ]);
    }
    
    /**
     * Get image intelligence data
     */
    public function get_image_intelligence($request) {
        // Placeholder for image intelligence features
        return rest_ensure_response([
            'total_images' => rand(1000, 5000),
            'with_vectors' => rand(800, 4000),
            'avg_quality_score' => rand(75, 90),
            'processing_queue' => rand(10, 100)
        ]);
    }
    
    /**
     * Subscribe to real-time updates
     */
    public function subscribe_to_updates($request) {
        // This would integrate with WebSocket server
        return rest_ensure_response([
            'success' => true,
            'message' => 'Subscribed to real-time updates'
        ]);
    }
}
