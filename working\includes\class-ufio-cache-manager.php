<?php
/**
 * UFIO Cache Manager
 * Advanced caching with Redis support and intelligent invalidation
 */

if (!defined('ABSPATH')) {
    exit;
}

class UFIO_Cache_Manager {
    
    private static $instance = null;
    private $redis = null;
    private $redis_available = false;
    private $default_ttl = 86400; // 24 hours
    private $cache_prefix = 'ufio:';
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        $this->init_redis();
        
        // Hook into post updates for cache invalidation
        add_action('save_post', [$this, 'invalidate_post_cache'], 10, 2);
        add_action('delete_post', [$this, 'invalidate_post_cache'], 10, 2);
        add_action('wp_insert_post', [$this, 'invalidate_post_cache'], 10, 2);
        
        // Hook into attachment updates
        add_action('add_attachment', [$this, 'invalidate_image_cache']);
        add_action('delete_attachment', [$this, 'invalidate_image_cache']);
        add_action('wp_update_attachment_metadata', [$this, 'invalidate_image_cache']);
        
        // Schedule cache cleanup
        add_action('ufio_cache_cleanup', [$this, 'cleanup_expired_cache']);
        if (!wp_next_scheduled('ufio_cache_cleanup')) {
            wp_schedule_event(time(), 'hourly', 'ufio_cache_cleanup');
        }
    }
    
    /**
     * Initialize Redis connection
     */
    private function init_redis() {
        if (!class_exists('Redis')) {
            return;
        }
        
        try {
            $this->redis = new Redis();
            
            $redis_config = $this->get_redis_config();
            $connected = $this->redis->connect(
                $redis_config['host'],
                $redis_config['port'],
                $redis_config['timeout']
            );
            
            if ($connected) {
                if (!empty($redis_config['password'])) {
                    $this->redis->auth($redis_config['password']);
                }
                
                if (!empty($redis_config['database'])) {
                    $this->redis->select($redis_config['database']);
                }
                
                $this->redis_available = true;
                
                // Set key prefix
                $this->redis->setOption(Redis::OPT_PREFIX, $this->cache_prefix);
            }
            
        } catch (Exception $e) {
            error_log('UFIO Redis connection failed: ' . $e->getMessage());
            $this->redis_available = false;
        }
    }
    
    /**
     * Get Redis configuration
     */
    private function get_redis_config() {
        $options = get_option('ufio_options', []);
        
        return [
            'host' => $options['redis_host'] ?? '127.0.0.1',
            'port' => $options['redis_port'] ?? 6379,
            'password' => $options['redis_password'] ?? '',
            'database' => $options['redis_database'] ?? 0,
            'timeout' => $options['redis_timeout'] ?? 2.5
        ];
    }
    
    /**
     * Store data in cache
     */
    public function set($key, $data, $ttl = null) {
        $ttl = $ttl ?? $this->default_ttl;
        $serialized_data = serialize($data);
        
        // Try Redis first
        if ($this->redis_available) {
            try {
                $result = $this->redis->setex($key, $ttl, $serialized_data);
                if ($result) {
                    $this->log_cache_operation('redis_set', $key, strlen($serialized_data));
                    return true;
                }
            } catch (Exception $e) {
                error_log('UFIO Redis set failed: ' . $e->getMessage());
                $this->redis_available = false;
            }
        }
        
        // Fallback to WordPress object cache
        $wp_result = wp_cache_set($key, $data, 'ufio', $ttl);
        if ($wp_result) {
            $this->log_cache_operation('wp_cache_set', $key, strlen($serialized_data));
        }
        
        // Fallback to transients
        $transient_result = set_transient($this->cache_prefix . $key, $data, $ttl);
        if ($transient_result) {
            $this->log_cache_operation('transient_set', $key, strlen($serialized_data));
        }
        
        // Fallback to post meta for post-specific data
        if (strpos($key, 'post_') === 0) {
            $post_id = str_replace('post_', '', explode('_', $key)[0]);
            if (is_numeric($post_id)) {
                update_post_meta($post_id, '_ufio_cache_' . $key, [
                    'data' => $data,
                    'expires' => time() + $ttl
                ]);
                $this->log_cache_operation('post_meta_set', $key, strlen($serialized_data));
            }
        }
        
        return $wp_result || $transient_result;
    }
    
    /**
     * Retrieve data from cache
     */
    public function get($key) {
        // Try Redis first
        if ($this->redis_available) {
            try {
                $data = $this->redis->get($key);
                if ($data !== false) {
                    $unserialized = unserialize($data);
                    $this->log_cache_operation('redis_hit', $key, strlen($data));
                    return $unserialized;
                }
            } catch (Exception $e) {
                error_log('UFIO Redis get failed: ' . $e->getMessage());
                $this->redis_available = false;
            }
        }
        
        // Try WordPress object cache
        $data = wp_cache_get($key, 'ufio');
        if ($data !== false) {
            $this->log_cache_operation('wp_cache_hit', $key, strlen(serialize($data)));
            return $data;
        }
        
        // Try transients
        $data = get_transient($this->cache_prefix . $key);
        if ($data !== false) {
            // Store in object cache for faster access
            wp_cache_set($key, $data, 'ufio', $this->default_ttl);
            $this->log_cache_operation('transient_hit', $key, strlen(serialize($data)));
            return $data;
        }
        
        // Try post meta for post-specific data
        if (strpos($key, 'post_') === 0) {
            $post_id = str_replace('post_', '', explode('_', $key)[0]);
            if (is_numeric($post_id)) {
                $cached = get_post_meta($post_id, '_ufio_cache_' . $key, true);
                if ($cached && isset($cached['expires']) && $cached['expires'] > time()) {
                    $data = $cached['data'];
                    // Promote to higher cache levels
                    wp_cache_set($key, $data, 'ufio', $this->default_ttl);
                    set_transient($this->cache_prefix . $key, $data, $this->default_ttl);
                    $this->log_cache_operation('post_meta_hit', $key, strlen(serialize($data)));
                    return $data;
                }
            }
        }
        
        $this->log_cache_operation('cache_miss', $key, 0);
        return false;
    }
    
    /**
     * Delete data from cache
     */
    public function delete($key) {
        $deleted = false;
        
        // Delete from Redis
        if ($this->redis_available) {
            try {
                $this->redis->del($key);
                $deleted = true;
            } catch (Exception $e) {
                error_log('UFIO Redis delete failed: ' . $e->getMessage());
            }
        }
        
        // Delete from WordPress object cache
        wp_cache_delete($key, 'ufio');
        
        // Delete from transients
        delete_transient($this->cache_prefix . $key);
        
        // Delete from post meta
        if (strpos($key, 'post_') === 0) {
            $post_id = str_replace('post_', '', explode('_', $key)[0]);
            if (is_numeric($post_id)) {
                delete_post_meta($post_id, '_ufio_cache_' . $key);
            }
        }
        
        $this->log_cache_operation('cache_delete', $key, 0);
        return $deleted;
    }
    
    /**
     * Store AI analysis with intelligent caching
     */
    public function set_ai_analysis($post_id, $analysis) {
        $key = "ai_analysis_{$post_id}";
        
        // Store with 30-day TTL for AI analysis
        $this->set($key, $analysis, 30 * DAY_IN_SECONDS);
        
        // Also store content hash for invalidation
        $post = get_post($post_id);
        if ($post) {
            $content_hash = md5($post->post_title . $post->post_content . $post->post_modified);
            $this->set("content_hash_{$post_id}", $content_hash, 30 * DAY_IN_SECONDS);
        }
    }
    
    /**
     * Get AI analysis with content validation
     */
    public function get_ai_analysis($post_id) {
        $post = get_post($post_id);
        if (!$post) {
            return false;
        }
        
        // Check if content has changed
        $current_hash = md5($post->post_title . $post->post_content . $post->post_modified);
        $cached_hash = $this->get("content_hash_{$post_id}");
        
        if ($cached_hash && $cached_hash !== $current_hash) {
            // Content changed, invalidate cache
            $this->delete("ai_analysis_{$post_id}");
            $this->delete("content_hash_{$post_id}");
            return false;
        }
        
        return $this->get("ai_analysis_{$post_id}");
    }
    
    /**
     * Store image embeddings
     */
    public function set_image_embedding($attachment_id, $embedding, $metadata = []) {
        $key = "image_embedding_{$attachment_id}";
        
        $data = [
            'embedding' => $embedding,
            'metadata' => $metadata,
            'generated_at' => time()
        ];
        
        // Store with 90-day TTL for embeddings
        $this->set($key, $data, 90 * DAY_IN_SECONDS);
        
        // Also store in database for persistence
        global $wpdb;
        $image_cache_table = $wpdb->prefix . 'ufio_image_cache';
        
        $wpdb->replace($image_cache_table, [
            'attachment_id' => $attachment_id,
            'clip_embedding' => json_encode($embedding),
            'keywords_json' => json_encode($metadata['keywords'] ?? []),
            'seo_score' => $metadata['seo_score'] ?? null,
            'last_analyzed' => current_time('mysql')
        ]);
    }
    
    /**
     * Get image embedding
     */
    public function get_image_embedding($attachment_id) {
        $key = "image_embedding_{$attachment_id}";
        $cached = $this->get($key);
        
        if ($cached !== false) {
            return $cached;
        }
        
        // Try database fallback
        global $wpdb;
        $image_cache_table = $wpdb->prefix . 'ufio_image_cache';
        
        $row = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $image_cache_table WHERE attachment_id = %d",
            $attachment_id
        ), ARRAY_A);
        
        if ($row && $row['clip_embedding']) {
            $data = [
                'embedding' => json_decode($row['clip_embedding'], true),
                'metadata' => [
                    'keywords' => json_decode($row['keywords_json'], true),
                    'seo_score' => $row['seo_score']
                ],
                'generated_at' => strtotime($row['last_analyzed'])
            ];
            
            // Promote to cache
            $this->set($key, $data, 90 * DAY_IN_SECONDS);
            return $data;
        }
        
        return false;
    }
    
    /**
     * Invalidate post-related cache
     */
    public function invalidate_post_cache($post_id, $post = null) {
        if (!$post) {
            $post = get_post($post_id);
        }
        
        if (!$post || !in_array($post->post_type, ['post', 'page'])) {
            return;
        }
        
        // Invalidate AI analysis
        $this->delete("ai_analysis_{$post_id}");
        $this->delete("content_hash_{$post_id}");
        
        // Invalidate related caches
        $this->delete("post_stats_{$post_id}");
        $this->delete("seo_analysis_{$post_id}");
        $this->delete("image_search_{$post_id}");
        
        // Invalidate batch caches that might contain this post
        $this->invalidate_batch_caches_for_post($post_id);
    }
    
    /**
     * Invalidate image-related cache
     */
    public function invalidate_image_cache($attachment_id) {
        if (!wp_attachment_is_image($attachment_id)) {
            return;
        }
        
        $this->delete("image_embedding_{$attachment_id}");
        $this->delete("image_analysis_{$attachment_id}");
        $this->delete("image_seo_{$attachment_id}");
        
        // Clear from database cache
        global $wpdb;
        $image_cache_table = $wpdb->prefix . 'ufio_image_cache';
        $wpdb->delete($image_cache_table, ['attachment_id' => $attachment_id]);
    }
    
    /**
     * Invalidate batch caches containing a specific post
     */
    private function invalidate_batch_caches_for_post($post_id) {
        global $wpdb;
        
        $batch_cache_table = $wpdb->prefix . 'ufio_batch_cache';
        
        // Find batch caches containing this post
        $batches = $wpdb->get_results($wpdb->prepare(
            "SELECT cache_key FROM $batch_cache_table WHERE post_ids LIKE %s",
            '%"' . $post_id . '"%'
        ));
        
        foreach ($batches as $batch) {
            $this->delete($batch->cache_key);
            $wpdb->delete($batch_cache_table, ['cache_key' => $batch->cache_key]);
        }
    }
    
    /**
     * Cleanup expired cache entries
     */
    public function cleanup_expired_cache() {
        // Clean up Redis expired keys (Redis handles this automatically)
        
        // Clean up database cache
        global $wpdb;
        
        // Clean expired batch cache
        $batch_cache_table = $wpdb->prefix . 'ufio_batch_cache';
        $wpdb->query("DELETE FROM $batch_cache_table WHERE expires_at < NOW()");
        
        // Clean expired post meta cache
        $wpdb->query("
            DELETE FROM {$wpdb->postmeta} 
            WHERE meta_key LIKE '_ufio_cache_%' 
            AND meta_value LIKE '%expires%' 
            AND CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(meta_value, '\"expires\";i:', -1), ';', 1) AS UNSIGNED) < UNIX_TIMESTAMP()
        ");
        
        // Clean old transients
        $wpdb->query($wpdb->prepare(
            "DELETE FROM {$wpdb->options} 
             WHERE option_name LIKE %s 
             AND option_name LIKE %s",
            '_transient_timeout_' . $this->cache_prefix . '%',
            '%'
        ));
    }
    
    /**
     * Get cache statistics
     */
    public function get_cache_stats() {
        $stats = [
            'redis_available' => $this->redis_available,
            'redis_info' => null,
            'wp_cache_enabled' => wp_using_ext_object_cache(),
            'transient_count' => 0,
            'post_meta_cache_count' => 0
        ];
        
        // Redis stats
        if ($this->redis_available) {
            try {
                $info = $this->redis->info();
                $stats['redis_info'] = [
                    'used_memory' => $info['used_memory_human'] ?? 'Unknown',
                    'connected_clients' => $info['connected_clients'] ?? 0,
                    'total_commands_processed' => $info['total_commands_processed'] ?? 0
                ];
            } catch (Exception $e) {
                $stats['redis_available'] = false;
            }
        }
        
        // Count transients
        global $wpdb;
        $stats['transient_count'] = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$wpdb->options} WHERE option_name LIKE %s",
            '_transient_' . $this->cache_prefix . '%'
        ));
        
        // Count post meta cache
        $stats['post_meta_cache_count'] = $wpdb->get_var(
            "SELECT COUNT(*) FROM {$wpdb->postmeta} WHERE meta_key LIKE '_ufio_cache_%'"
        );
        
        return $stats;
    }
    
    /**
     * Clear all UFIO caches
     */
    public function clear_all_cache() {
        // Clear Redis
        if ($this->redis_available) {
            try {
                $this->redis->flushDB();
            } catch (Exception $e) {
                error_log('UFIO Redis flush failed: ' . $e->getMessage());
            }
        }
        
        // Clear WordPress object cache
        wp_cache_flush();
        
        // Clear transients
        global $wpdb;
        $wpdb->query($wpdb->prepare(
            "DELETE FROM {$wpdb->options} WHERE option_name LIKE %s OR option_name LIKE %s",
            '_transient_' . $this->cache_prefix . '%',
            '_transient_timeout_' . $this->cache_prefix . '%'
        ));
        
        // Clear post meta cache
        $wpdb->query("DELETE FROM {$wpdb->postmeta} WHERE meta_key LIKE '_ufio_cache_%'");
        
        // Clear database cache tables
        $batch_cache_table = $wpdb->prefix . 'ufio_batch_cache';
        $image_cache_table = $wpdb->prefix . 'ufio_image_cache';
        
        $wpdb->query("TRUNCATE TABLE $batch_cache_table");
        $wpdb->query("TRUNCATE TABLE $image_cache_table");
    }
    
    /**
     * Log cache operations for analytics
     */
    private function log_cache_operation($operation, $key, $size) {
        // Only log in debug mode to avoid performance impact
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("UFIO Cache: $operation - $key - {$size} bytes");
        }
    }
}
