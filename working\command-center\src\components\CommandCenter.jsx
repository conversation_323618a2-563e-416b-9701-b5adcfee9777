import React, { Suspense, lazy, useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useWebSocket } from '@hooks/useWebSocket';
import { useTheme } from '@hooks/useTheme';
import { useAccessibility } from '@hooks/useAccessibility';
import { Header } from './Header';
import { Sidebar } from './Sidebar';
import { LoadingSpinner } from './LoadingSpinner';
import { ErrorBoundary } from './ErrorBoundary';
import styles from './CommandCenter.module.css';

// Lazy load heavy components for optimal performance
const Dashboard = lazy(() => import('./Dashboard'));
const PostsHeatMap = lazy(() => import('./PostsHeatMap'));
const ImageIntelligence = lazy(() => import('./ImageIntelligence'));
const Analytics = lazy(() => import('./Analytics'));
const Settings = lazy(() => import('./Settings'));
const SystemStatus = lazy(() => import('./SystemStatus'));

const VIEWS = {
  dashboard: { component: Dashboard, title: 'Command Center', icon: '🎛️' },
  heatmap: { component: PostsHeatMap, title: 'Traffic × SEO Heat Map', icon: '🔥' },
  intelligence: { component: ImageIntelligence, title: 'Image Intelligence', icon: '🧠' },
  analytics: { component: Analytics, title: 'Analytics', icon: '📊' },
  settings: { component: Settings, title: 'Settings', icon: '⚙️' },
  system: { component: SystemStatus, title: 'System Status', icon: '🔧' }
};

export function CommandCenter() {
  const [activeView, setActiveView] = useState('dashboard');
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  
  const { theme, toggleTheme } = useTheme();
  const { reducedMotion, highContrast } = useAccessibility();
  const { isConnected, lastMessage, sendMessage } = useWebSocket();
  
  // Initialize command center
  useEffect(() => {
    const initializeCommandCenter = async () => {
      try {
        // Preload critical data
        await Promise.all([
          fetch(`${window.UFIOCommandCenter.apiUrl}ufio/v1/stats`),
          fetch(`${window.UFIOCommandCenter.apiUrl}ufio/v1/system-status`)
        ]);
        
        setIsLoading(false);
      } catch (error) {
        console.error('Failed to initialize Command Center:', error);
        setIsLoading(false);
      }
    };
    
    initializeCommandCenter();
  }, []);
  
  // Handle real-time updates
  useEffect(() => {
    if (lastMessage) {
      const { type, data } = lastMessage;
      
      switch (type) {
        case 'processing_update':
          // Handle processing updates
          break;
        case 'system_alert':
          // Handle system alerts
          break;
        case 'stats_update':
          // Handle stats updates
          break;
        default:
          break;
      }
    }
  }, [lastMessage]);
  
  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyboard = (event) => {
      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case '1':
            event.preventDefault();
            setActiveView('dashboard');
            break;
          case '2':
            event.preventDefault();
            setActiveView('heatmap');
            break;
          case '3':
            event.preventDefault();
            setActiveView('intelligence');
            break;
          case '4':
            event.preventDefault();
            setActiveView('analytics');
            break;
          case 'd':
            event.preventDefault();
            toggleTheme();
            break;
          case 'b':
            event.preventDefault();
            setSidebarCollapsed(!sidebarCollapsed);
            break;
          default:
            break;
        }
      }
    };
    
    window.addEventListener('keydown', handleKeyboard);
    return () => window.removeEventListener('keydown', handleKeyboard);
  }, [sidebarCollapsed, toggleTheme]);
  
  const CurrentView = VIEWS[activeView]?.component || Dashboard;
  
  if (isLoading) {
    return (
      <div className={styles.loadingContainer}>
        <LoadingSpinner size="large" />
        <p>Initializing Command Center...</p>
      </div>
    );
  }
  
  return (
    <div 
      className={`${styles.commandCenter} ${styles[theme]} ${highContrast ? styles.highContrast : ''}`}
      data-reduced-motion={reducedMotion}
    >
      <Header
        activeView={activeView}
        viewTitle={VIEWS[activeView]?.title || 'Command Center'}
        isConnected={isConnected}
        onToggleTheme={toggleTheme}
        onToggleSidebar={() => setSidebarCollapsed(!sidebarCollapsed)}
        sidebarCollapsed={sidebarCollapsed}
      />
      
      <div className={styles.mainContainer}>
        <AnimatePresence>
          {!sidebarCollapsed && (
            <motion.aside
              className={styles.sidebar}
              initial={{ width: 0, opacity: 0 }}
              animate={{ width: 280, opacity: 1 }}
              exit={{ width: 0, opacity: 0 }}
              transition={{ 
                duration: reducedMotion ? 0 : 0.3,
                ease: 'easeInOut'
              }}
            >
              <Sidebar
                activeView={activeView}
                onViewChange={setActiveView}
                views={VIEWS}
                isConnected={isConnected}
              />
            </motion.aside>
          )}
        </AnimatePresence>
        
        <main 
          className={styles.mainContent}
          style={{
            marginLeft: sidebarCollapsed ? 0 : 280
          }}
        >
          <ErrorBoundary>
            <Suspense 
              fallback={
                <div className={styles.viewLoading}>
                  <LoadingSpinner />
                  <p>Loading {VIEWS[activeView]?.title}...</p>
                </div>
              }
            >
              <AnimatePresence mode="wait">
                <motion.div
                  key={activeView}
                  className={styles.viewContainer}
                  initial={{ opacity: 0, y: reducedMotion ? 0 : 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: reducedMotion ? 0 : -20 }}
                  transition={{ 
                    duration: reducedMotion ? 0 : 0.2,
                    ease: 'easeInOut'
                  }}
                >
                  <CurrentView />
                </motion.div>
              </AnimatePresence>
            </Suspense>
          </ErrorBoundary>
        </main>
      </div>
      
      {/* Global keyboard shortcuts help */}
      <div className={styles.keyboardShortcuts} aria-hidden="true">
        <kbd>Ctrl+1-4</kbd> Switch views • <kbd>Ctrl+D</kbd> Toggle theme • <kbd>Ctrl+B</kbd> Toggle sidebar
      </div>
    </div>
  );
}
