/**
 * 🚀 UFIO PRO - PROFESSIONAL ADMIN STYLES
 * 
 * Ultra-modern, professional-grade CSS for WordPress admin
 * Built for maximum beauty, performance, and user experience
 * 
 * @package UFIO_Pro
 * @version 9.0.0
 * <AUTHOR> Pro Team
 */

/* ===== PROFESSIONAL DESIGN TOKENS ===== */
:root {
  /* Primary Colors */
  --ufio-primary: #0073aa;
  --ufio-primary-light: #005a87;
  --ufio-primary-dark: #004a6b;
  
  /* Success Colors */
  --ufio-success: #00a32a;
  --ufio-success-light: #00d084;
  --ufio-success-dark: #007c20;
  
  /* Warning Colors */
  --ufio-warning: #ff6900;
  --ufio-warning-light: #ff8500;
  --ufio-warning-dark: #e55100;
  
  /* Error Colors */
  --ufio-error: #d63638;
  --ufio-error-light: #ff4444;
  --ufio-error-dark: #b32d2e;
  
  /* Neutral Colors */
  --ufio-gray-50: #f9fafb;
  --ufio-gray-100: #f3f4f6;
  --ufio-gray-200: #e5e7eb;
  --ufio-gray-300: #d1d5db;
  --ufio-gray-400: #9ca3af;
  --ufio-gray-500: #6b7280;
  --ufio-gray-600: #4b5563;
  --ufio-gray-700: #374151;
  --ufio-gray-800: #1f2937;
  --ufio-gray-900: #111827;
  
  /* Gradients */
  --ufio-gradient-primary: linear-gradient(135deg, #0073aa 0%, #005a87 100%);
  --ufio-gradient-success: linear-gradient(135deg, #00a32a 0%, #00d084 100%);
  --ufio-gradient-warning: linear-gradient(135deg, #ff6900 0%, #ff8500 100%);
  --ufio-gradient-premium: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  
  /* Shadows */
  --ufio-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --ufio-shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --ufio-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --ufio-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --ufio-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Spacing */
  --ufio-space-1: 0.25rem;
  --ufio-space-2: 0.5rem;
  --ufio-space-3: 0.75rem;
  --ufio-space-4: 1rem;
  --ufio-space-5: 1.25rem;
  --ufio-space-6: 1.5rem;
  --ufio-space-8: 2rem;
  --ufio-space-10: 2.5rem;
  --ufio-space-12: 3rem;
  
  /* Border Radius */
  --ufio-radius-sm: 0.125rem;
  --ufio-radius-base: 0.25rem;
  --ufio-radius-md: 0.375rem;
  --ufio-radius-lg: 0.5rem;
  --ufio-radius-xl: 0.75rem;
  --ufio-radius-2xl: 1rem;
  
  /* Transitions */
  --ufio-transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --ufio-transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  --ufio-transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===== PROFESSIONAL BASE STYLES ===== */
.ufio-pro-wrapper {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: var(--ufio-gray-50);
  min-height: 100vh;
  padding: var(--ufio-space-6);
}

.ufio-pro-header {
  background: var(--ufio-gradient-premium);
  color: white;
  padding: var(--ufio-space-8);
  border-radius: var(--ufio-radius-2xl);
  margin-bottom: var(--ufio-space-8);
  box-shadow: var(--ufio-shadow-lg);
  position: relative;
  overflow: hidden;
}

.ufio-pro-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.ufio-pro-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 var(--ufio-space-4) 0;
  position: relative;
  z-index: 1;
}

.ufio-pro-header .subtitle {
  font-size: 1.125rem;
  opacity: 0.9;
  margin: 0;
  position: relative;
  z-index: 1;
}

/* ===== PROFESSIONAL STATISTICS GRID ===== */
.ufio-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--ufio-space-6);
  margin: var(--ufio-space-8) 0;
}

.stat-card {
  background: white;
  padding: var(--ufio-space-8);
  border-radius: var(--ufio-radius-xl);
  box-shadow: var(--ufio-shadow-base);
  text-align: center;
  transition: var(--ufio-transition-normal);
  border: 1px solid var(--ufio-gray-200);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--ufio-gradient-primary);
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--ufio-shadow-xl);
}

.stat-card div:first-child {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: var(--ufio-space-2);
}

.stat-card div:last-child {
  color: var(--ufio-gray-600);
  font-weight: 500;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* ===== PROFESSIONAL ACTIONS GRID ===== */
.ufio-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--ufio-space-6);
  margin: var(--ufio-space-8) 0;
}

.action-card {
  background: white;
  padding: var(--ufio-space-8);
  border-radius: var(--ufio-radius-xl);
  box-shadow: var(--ufio-shadow-base);
  transition: var(--ufio-transition-normal);
  border: 1px solid var(--ufio-gray-200);
  position: relative;
}

.action-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--ufio-shadow-lg);
}

.action-card h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 var(--ufio-space-4) 0;
  color: var(--ufio-gray-900);
}

.action-card p {
  color: var(--ufio-gray-600);
  margin: 0 0 var(--ufio-space-6) 0;
  line-height: 1.6;
}

/* ===== PROFESSIONAL BUTTONS ===== */
.button.button-large {
  padding: var(--ufio-space-4) var(--ufio-space-8);
  font-size: 1rem;
  font-weight: 600;
  border-radius: var(--ufio-radius-lg);
  transition: var(--ufio-transition-normal);
  border: none;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--ufio-space-2);
  text-decoration: none;
  width: 100%;
}

.button.button-primary.button-large {
  background: var(--ufio-gradient-primary);
  color: white;
  box-shadow: var(--ufio-shadow-sm);
}

.button.button-primary.button-large:hover {
  transform: translateY(-2px);
  box-shadow: var(--ufio-shadow-md);
}

.button.button-secondary.button-large {
  background: white;
  color: var(--ufio-gray-700);
  border: 2px solid var(--ufio-gray-300);
  box-shadow: var(--ufio-shadow-sm);
}

.button.button-secondary.button-large:hover {
  background: var(--ufio-gray-50);
  border-color: var(--ufio-gray-400);
  transform: translateY(-1px);
  box-shadow: var(--ufio-shadow-md);
}

/* ===== PROFESSIONAL RESULTS CONTAINER ===== */
#ufio-results {
  margin-top: var(--ufio-space-8);
}

#ufio-results > div {
  background: white;
  padding: var(--ufio-space-8);
  border-radius: var(--ufio-radius-xl);
  box-shadow: var(--ufio-shadow-base);
  border: 1px solid var(--ufio-gray-200);
  animation: slideInUp 0.5s ease-out;
}

#ufio-results p {
  margin: 0;
  font-size: 1.125rem;
  line-height: 1.6;
}

/* ===== PROFESSIONAL ANIMATIONS ===== */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .ufio-pro-wrapper {
    padding: var(--ufio-space-4);
  }
  
  .ufio-pro-header {
    padding: var(--ufio-space-6);
  }
  
  .ufio-pro-header h1 {
    font-size: 2rem;
  }
  
  .ufio-stats {
    grid-template-columns: 1fr;
    gap: var(--ufio-space-4);
  }
  
  .ufio-actions {
    grid-template-columns: 1fr;
    gap: var(--ufio-space-4);
  }
  
  .stat-card,
  .action-card {
    padding: var(--ufio-space-6);
  }
}

@media (max-width: 480px) {
  .ufio-pro-header h1 {
    font-size: 1.75rem;
  }
  
  .stat-card,
  .action-card {
    padding: var(--ufio-space-4);
  }
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus styles for keyboard navigation */
.button:focus,
.action-card:focus {
  outline: 2px solid var(--ufio-primary);
  outline-offset: 2px;
}

/* ===== LOADING STATES ===== */
.ufio-loading {
  position: relative;
}

.ufio-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid var(--ufio-gray-300);
  border-radius: 50%;
  border-top-color: var(--ufio-primary);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* ===== PROFESSIONAL POLISH ===== */
.ufio-pro-wrapper * {
  box-sizing: border-box;
}

.ufio-pro-wrapper h1,
.ufio-pro-wrapper h2,
.ufio-pro-wrapper h3 {
  font-weight: 600;
  line-height: 1.2;
}

.ufio-pro-wrapper p {
  line-height: 1.6;
}

/* Success/Error message styling */
#ufio-results p:contains("✅") {
  color: var(--ufio-success);
}

#ufio-results p:contains("❌") {
  color: var(--ufio-error);
}

#ufio-results p:contains("🔄") {
  color: var(--ufio-primary);
  animation: pulse 2s infinite;
}
