/**
 * 🚀 UFIO ENTERPRISE PRO - REVOLUTIONARY COMPONENT LIBRARY
 * 
 * Ultra-modern, professional-grade UI components with quantum interactions
 * Built for enterprise-level WordPress plugins with advanced animations
 * 
 * @package UFIO_Enterprise_Pro
 * @version 10.0.0
 * <AUTHOR> Enterprise Design Team
 */

/* ===== QUANTUM BUTTON SYSTEM ===== */
.ufio-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--ufio-space-2);
  padding: var(--ufio-space-3) var(--ufio-space-6);
  font-family: var(--ufio-font-family-primary);
  font-size: var(--ufio-text-sm);
  font-weight: var(--ufio-font-weight-semibold);
  line-height: 1;
  text-decoration: none;
  border: 1px solid transparent;
  border-radius: var(--ufio-radius-lg);
  cursor: pointer;
  transition: var(--ufio-transition-normal);
  position: relative;
  overflow: hidden;
  user-select: none;
  white-space: nowrap;
}

.ufio-btn:focus {
  outline: 2px solid var(--ufio-primary-400);
  outline-offset: 2px;
}

.ufio-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* Button Sizes */
.ufio-btn-xs {
  padding: var(--ufio-space-1) var(--ufio-space-3);
  font-size: var(--ufio-text-xs);
}

.ufio-btn-sm {
  padding: var(--ufio-space-2) var(--ufio-space-4);
  font-size: var(--ufio-text-sm);
}

.ufio-btn-lg {
  padding: var(--ufio-space-4) var(--ufio-space-8);
  font-size: var(--ufio-text-lg);
}

.ufio-btn-xl {
  padding: var(--ufio-space-5) var(--ufio-space-10);
  font-size: var(--ufio-text-xl);
}

/* Primary Button - Enterprise Blue */
.ufio-btn-primary {
  background: var(--ufio-gradient-primary);
  color: white;
  border-color: var(--ufio-primary-600);
  box-shadow: var(--ufio-shadow-sm);
}

.ufio-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--ufio-shadow-lg);
}

.ufio-btn-primary:active {
  transform: translateY(0);
  box-shadow: var(--ufio-shadow-sm);
}

/* Success Button - Growth Green */
.ufio-btn-success {
  background: var(--ufio-gradient-success);
  color: white;
  border-color: var(--ufio-success-600);
  box-shadow: var(--ufio-shadow-sm);
}

.ufio-btn-success:hover {
  transform: translateY(-2px);
  box-shadow: var(--ufio-shadow-lg);
}

/* Warning Button - Attention Orange */
.ufio-btn-warning {
  background: var(--ufio-gradient-warning);
  color: white;
  border-color: var(--ufio-warning-600);
  box-shadow: var(--ufio-shadow-sm);
}

.ufio-btn-warning:hover {
  transform: translateY(-2px);
  box-shadow: var(--ufio-shadow-lg);
}

/* Quantum Button - Premium Effect */
.ufio-btn-quantum {
  background: var(--ufio-gradient-quantum);
  color: var(--ufio-gray-800);
  border: none;
  box-shadow: var(--ufio-shadow-quantum);
  position: relative;
}

.ufio-btn-quantum::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s;
}

.ufio-btn-quantum:hover::before {
  left: 100%;
}

.ufio-btn-quantum:hover {
  transform: translateY(-3px);
  box-shadow: var(--ufio-shadow-2xl);
}

/* Ghost Button */
.ufio-btn-ghost {
  background: transparent;
  color: var(--ufio-gray-700);
  border-color: var(--ufio-gray-300);
}

.ufio-btn-ghost:hover {
  background: var(--ufio-gray-50);
  border-color: var(--ufio-gray-400);
  transform: translateY(-1px);
}

/* ===== ENTERPRISE FORM COMPONENTS ===== */
.ufio-form-group {
  margin-bottom: var(--ufio-space-6);
}

.ufio-label {
  display: block;
  font-size: var(--ufio-text-sm);
  font-weight: var(--ufio-font-weight-semibold);
  color: var(--ufio-gray-700);
  margin-bottom: var(--ufio-space-2);
}

.ufio-input,
.ufio-select,
.ufio-textarea {
  width: 100%;
  padding: var(--ufio-space-3) var(--ufio-space-4);
  font-family: var(--ufio-font-family-primary);
  font-size: var(--ufio-text-base);
  color: var(--ufio-gray-900);
  background: white;
  border: 1px solid var(--ufio-gray-300);
  border-radius: var(--ufio-radius-lg);
  transition: var(--ufio-transition-normal);
}

.ufio-input:focus,
.ufio-select:focus,
.ufio-textarea:focus {
  outline: none;
  border-color: var(--ufio-primary-400);
  box-shadow: 0 0 0 3px rgba(0, 102, 255, 0.1);
}

.ufio-textarea {
  resize: vertical;
  min-height: 120px;
}

/* ===== QUANTUM PROGRESS INDICATORS ===== */
.ufio-progress {
  width: 100%;
  height: 8px;
  background: var(--ufio-gray-200);
  border-radius: var(--ufio-radius-full);
  overflow: hidden;
  position: relative;
}

.ufio-progress-bar {
  height: 100%;
  background: var(--ufio-gradient-primary);
  border-radius: var(--ufio-radius-full);
  transition: width var(--ufio-transition-slow);
  position: relative;
}

.ufio-progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.6),
    transparent
  );
  animation: ufio-progress-shine 2s infinite;
}

@keyframes ufio-progress-shine {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Circular Progress */
.ufio-progress-circle {
  width: 60px;
  height: 60px;
  position: relative;
}

.ufio-progress-circle svg {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
}

.ufio-progress-circle-bg {
  fill: none;
  stroke: var(--ufio-gray-200);
  stroke-width: 4;
}

.ufio-progress-circle-bar {
  fill: none;
  stroke: var(--ufio-primary-500);
  stroke-width: 4;
  stroke-linecap: round;
  transition: stroke-dashoffset var(--ufio-transition-slow);
}

/* ===== ENTERPRISE NOTIFICATIONS ===== */
.ufio-notification {
  position: fixed;
  top: var(--ufio-space-6);
  right: var(--ufio-space-6);
  max-width: 400px;
  background: white;
  border-radius: var(--ufio-radius-xl);
  box-shadow: var(--ufio-shadow-xl);
  border-left: 4px solid var(--ufio-primary-500);
  padding: var(--ufio-space-4);
  z-index: var(--ufio-z-toast);
  transform: translateX(100%);
  transition: var(--ufio-transition-quantum);
}

.ufio-notification.show {
  transform: translateX(0);
}

.ufio-notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--ufio-space-2);
}

.ufio-notification-title {
  font-weight: var(--ufio-font-weight-semibold);
  color: var(--ufio-gray-900);
}

.ufio-notification-close {
  background: none;
  border: none;
  font-size: var(--ufio-text-lg);
  color: var(--ufio-gray-400);
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--ufio-radius-base);
  transition: var(--ufio-transition-fast);
}

.ufio-notification-close:hover {
  background: var(--ufio-gray-100);
  color: var(--ufio-gray-600);
}

.ufio-notification-body {
  color: var(--ufio-gray-600);
  font-size: var(--ufio-text-sm);
  line-height: 1.5;
}

/* Notification Types */
.ufio-notification.success {
  border-left-color: var(--ufio-success-500);
}

.ufio-notification.warning {
  border-left-color: var(--ufio-warning-500);
}

.ufio-notification.error {
  border-left-color: var(--ufio-error-500);
}

/* ===== QUANTUM LOADING SPINNERS ===== */
.ufio-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--ufio-gray-200);
  border-radius: 50%;
  border-top-color: var(--ufio-primary-500);
  animation: ufio-spin 1s ease-in-out infinite;
}

.ufio-spinner-lg {
  width: 40px;
  height: 40px;
  border-width: 4px;
}

@keyframes ufio-spin {
  to { transform: rotate(360deg); }
}

/* Quantum Spinner */
.ufio-spinner-quantum {
  width: 40px;
  height: 40px;
  position: relative;
}

.ufio-spinner-quantum::before,
.ufio-spinner-quantum::after {
  content: '';
  position: absolute;
  border-radius: 50%;
  animation: ufio-quantum-pulse 2s infinite;
}

.ufio-spinner-quantum::before {
  width: 100%;
  height: 100%;
  background: var(--ufio-gradient-quantum);
  animation-delay: 0s;
}

.ufio-spinner-quantum::after {
  width: 80%;
  height: 80%;
  top: 10%;
  left: 10%;
  background: var(--ufio-gradient-primary);
  animation-delay: 0.5s;
}

@keyframes ufio-quantum-pulse {
  0%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
}

/* ===== ENTERPRISE MODALS ===== */
.ufio-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  z-index: var(--ufio-z-modal-backdrop);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: var(--ufio-transition-normal);
}

.ufio-modal-overlay.active {
  opacity: 1;
  visibility: visible;
}

.ufio-modal {
  background: white;
  border-radius: var(--ufio-radius-2xl);
  box-shadow: var(--ufio-shadow-2xl);
  max-width: 90vw;
  max-height: 90vh;
  overflow: hidden;
  transform: scale(0.9) translateY(20px);
  transition: var(--ufio-transition-quantum);
}

.ufio-modal-overlay.active .ufio-modal {
  transform: scale(1) translateY(0);
}

.ufio-modal-header {
  padding: var(--ufio-space-6);
  border-bottom: 1px solid var(--ufio-gray-200);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ufio-modal-title {
  font-size: var(--ufio-text-xl);
  font-weight: var(--ufio-font-weight-bold);
  color: var(--ufio-gray-900);
  margin: 0;
}

.ufio-modal-close {
  background: none;
  border: none;
  font-size: var(--ufio-text-2xl);
  color: var(--ufio-gray-400);
  cursor: pointer;
  padding: var(--ufio-space-2);
  border-radius: var(--ufio-radius-base);
  transition: var(--ufio-transition-fast);
}

.ufio-modal-close:hover {
  background: var(--ufio-gray-100);
  color: var(--ufio-gray-600);
}

.ufio-modal-body {
  padding: var(--ufio-space-6);
  max-height: 60vh;
  overflow-y: auto;
}

.ufio-modal-footer {
  padding: var(--ufio-space-6);
  border-top: 1px solid var(--ufio-gray-200);
  display: flex;
  justify-content: flex-end;
  gap: var(--ufio-space-3);
}

/* ===== ENTERPRISE STATISTICS CARDS ===== */
.ufio-stat-card {
  background: white;
  border-radius: var(--ufio-radius-xl);
  padding: var(--ufio-space-6);
  box-shadow: var(--ufio-shadow-base);
  border: 1px solid var(--ufio-gray-200);
  transition: var(--ufio-transition-normal);
  text-align: center;
}

.ufio-stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--ufio-shadow-lg);
}

.ufio-stat-number {
  font-size: var(--ufio-text-3xl);
  font-weight: var(--ufio-font-weight-bold);
  color: var(--ufio-primary-600);
  margin-bottom: var(--ufio-space-2);
}

.ufio-stat-label {
  font-size: var(--ufio-text-sm);
  color: var(--ufio-gray-600);
  font-weight: var(--ufio-font-weight-medium);
}

.ufio-stat-card.success .ufio-stat-number {
  color: var(--ufio-success-600);
}

.ufio-stat-card.warning .ufio-stat-number {
  color: var(--ufio-warning-600);
}

.ufio-stat-card.error .ufio-stat-number {
  color: var(--ufio-error-600);
}

/* ===== QUANTUM ANIMATIONS ===== */
@keyframes ufio-fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes ufio-slide-in-right {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes ufio-bounce-in {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.ufio-animate-fade-in {
  animation: ufio-fade-in 0.5s ease-out;
}

.ufio-animate-slide-in-right {
  animation: ufio-slide-in-right 0.5s ease-out;
}

.ufio-animate-bounce-in {
  animation: ufio-bounce-in 0.6s ease-out;
}
