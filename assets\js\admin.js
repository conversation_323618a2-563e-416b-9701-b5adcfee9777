/**
 * AI SEO Optimizer Ultra - Admin JavaScript
 * 
 * Professional admin interface functionality with modern ES6+ features
 * 
 * @package AiSeoOptimizerUltra
 * @since 9.0.0
 */

(function($) {
    'use strict';

    /**
     * Main Admin Class
     */
    class AiSeoUltraAdmin {
        constructor() {
            this.init();
        }

        /**
         * Initialize admin functionality
         */
        init() {
            this.bindEvents();
            this.initComponents();
            this.loadInitialData();
        }

        /**
         * Bind event listeners
         */
        bindEvents() {
            // Process single post
            $(document).on('click', '.ai-seo-process-post', this.processPost.bind(this));
            
            // Apply suggestions
            $(document).on('click', '.ai-seo-apply-suggestion', this.applySuggestion.bind(this));
            
            // Test API connection
            $(document).on('click', '.ai-seo-test-api', this.testApiConnection.bind(this));
            
            // Save settings
            $(document).on('click', '.ai-seo-save-settings', this.saveSettings.bind(this));
            
            // Bulk processing
            $(document).on('click', '.ai-seo-bulk-process', this.initBulkProcess.bind(this));
            
            // Real-time score updates
            $(document).on('input', '.ai-seo-field input, .ai-seo-field textarea', 
                this.debounce(this.updateScores.bind(this), 500));
        }

        /**
         * Initialize components
         */
        initComponents() {
            this.initTooltips();
            this.initProgressBars();
            this.initCharacterCounters();
        }

        /**
         * Load initial data
         */
        loadInitialData() {
            if (typeof aiSeoUltra !== 'undefined' && aiSeoUltra.postId) {
                this.loadPostScores(aiSeoUltra.postId);
            }
        }

        /**
         * Process single post
         */
        async processPost(e) {
            e.preventDefault();
            
            const $button = $(e.currentTarget);
            const postId = $button.data('post-id') || this.getCurrentPostId();
            
            if (!postId) {
                this.showNotice('No post ID found', 'error');
                return;
            }

            try {
                this.setButtonLoading($button, true);
                
                const response = await this.makeAjaxRequest('ai_seo_process_post', {
                    post_id: postId
                });

                if (response.success) {
                    this.showNotice(response.data.message || 'Post processed successfully', 'success');
                    this.updatePostData(response.data.data);
                } else {
                    this.showNotice(response.data || 'Processing failed', 'error');
                }
            } catch (error) {
                this.showNotice('An error occurred: ' + error.message, 'error');
            } finally {
                this.setButtonLoading($button, false);
            }
        }

        /**
         * Apply AI suggestion
         */
        applySuggestion(e) {
            e.preventDefault();
            
            const $button = $(e.currentTarget);
            const field = $button.data('field');
            const suggestion = $button.data('suggestion');
            
            if (!field || !suggestion) {
                return;
            }

            const $field = $(`[name="${field}"]`);
            if ($field.length) {
                $field.val(suggestion).trigger('input');
                this.showNotice(`${field} updated with AI suggestion`, 'success');
            }
        }

        /**
         * Test API connection
         */
        async testApiConnection(e) {
            e.preventDefault();
            
            const $button = $(e.currentTarget);
            
            try {
                this.setButtonLoading($button, true);
                
                const response = await this.makeAjaxRequest('ai_seo_test_api');
                
                if (response.success) {
                    this.showNotice(response.data.message || 'API connection successful', 'success');
                } else {
                    this.showNotice(response.data || 'API connection failed', 'error');
                }
            } catch (error) {
                this.showNotice('Connection test failed: ' + error.message, 'error');
            } finally {
                this.setButtonLoading($button, false);
            }
        }

        /**
         * Save settings
         */
        async saveSettings(e) {
            e.preventDefault();
            
            const $button = $(e.currentTarget);
            const $form = $button.closest('form');
            const formData = this.serializeFormData($form);
            
            try {
                this.setButtonLoading($button, true);
                
                const response = await this.makeAjaxRequest('ai_seo_save_settings', {
                    settings: formData
                });
                
                if (response.success) {
                    this.showNotice(response.data.message || 'Settings saved successfully', 'success');
                } else {
                    this.showNotice(response.data || 'Failed to save settings', 'error');
                }
            } catch (error) {
                this.showNotice('Save failed: ' + error.message, 'error');
            } finally {
                this.setButtonLoading($button, false);
            }
        }

        /**
         * Initialize bulk processing
         */
        initBulkProcess(e) {
            e.preventDefault();
            
            const selectedPosts = this.getSelectedPosts();
            if (selectedPosts.length === 0) {
                this.showNotice('Please select posts to process', 'warning');
                return;
            }

            if (confirm(`Process ${selectedPosts.length} posts? This may take a while.`)) {
                this.startBulkProcess(selectedPosts);
            }
        }

        /**
         * Start bulk processing
         */
        async startBulkProcess(postIds) {
            const $progressContainer = this.createProgressContainer();
            let processed = 0;
            const total = postIds.length;

            for (const postId of postIds) {
                try {
                    await this.makeAjaxRequest('ai_seo_process_post', { post_id: postId });
                    processed++;
                } catch (error) {
                    console.error(`Failed to process post ${postId}:`, error);
                }
                
                this.updateProgress($progressContainer, processed, total);
                
                // Small delay to prevent overwhelming the server
                await this.delay(100);
            }

            this.showNotice(`Bulk processing completed. ${processed}/${total} posts processed.`, 'success');
            $progressContainer.fadeOut();
        }

        /**
         * Update SEO scores in real-time
         */
        async updateScores() {
            const postId = this.getCurrentPostId();
            if (!postId) return;

            const title = $('[name="post_title"]').val() || '';
            const description = $('[name="ai_seo_meta_description"]').val() || '';

            // Calculate local scores
            const titleScore = this.calculateTitleScore(title);
            const descriptionScore = this.calculateDescriptionScore(description);

            this.updateScoreDisplay('title', titleScore);
            this.updateScoreDisplay('description', descriptionScore);
        }

        /**
         * Calculate title score (simplified)
         */
        calculateTitleScore(title) {
            let score = 0;
            const length = title.length;

            // Length score
            if (length >= 50 && length <= 60) score += 40;
            else if (length >= 40 && length <= 70) score += 30;
            else if (length >= 30 && length <= 80) score += 20;

            // Word count
            const wordCount = title.split(/\s+/).length;
            if (wordCount >= 5 && wordCount <= 12) score += 30;

            // Basic readability
            if (!/[!?]{2,}/.test(title)) score += 15;
            if (!/[A-Z]{3,}/.test(title)) score += 15;

            return Math.min(100, score);
        }

        /**
         * Calculate description score (simplified)
         */
        calculateDescriptionScore(description) {
            if (!description) return 0;

            let score = 0;
            const length = description.length;

            // Length score
            if (length >= 150 && length <= 160) score += 40;
            else if (length >= 140 && length <= 170) score += 35;
            else if (length >= 120 && length <= 180) score += 25;

            // Call to action
            const ctaPatterns = /\b(learn more|read more|discover|find out|get started)\b/i;
            if (ctaPatterns.test(description)) score += 20;

            // Sentence structure
            const sentences = description.split(/[.!?]+/).filter(s => s.trim().length > 0);
            if (sentences.length >= 2 && sentences.length <= 4) score += 20;

            // Avoid keyword stuffing (basic check)
            const words = description.toLowerCase().split(/\s+/);
            const uniqueWords = new Set(words);
            if (uniqueWords.size / words.length > 0.7) score += 20;

            return Math.min(100, score);
        }

        /**
         * Update score display
         */
        updateScoreDisplay(type, score) {
            const $scoreItem = $(`.ai-seo-score-item[data-type="${type}"]`);
            const $scoreValue = $scoreItem.find('.ai-seo-score-value');
            const $scoreFill = $scoreItem.find('.ai-seo-score-fill');

            // Update value
            $scoreValue.text(score);

            // Update progress bar
            $scoreFill.css('width', score + '%');

            // Update class
            $scoreItem.removeClass('score-poor score-fair score-good score-excellent');
            if (score >= 80) $scoreItem.addClass('score-excellent');
            else if (score >= 60) $scoreItem.addClass('score-good');
            else if (score >= 40) $scoreItem.addClass('score-fair');
            else $scoreItem.addClass('score-poor');
        }

        /**
         * Load post scores
         */
        async loadPostScores(postId) {
            try {
                const response = await this.makeAjaxRequest('ai_seo_get_scores', {
                    post_ids: [postId]
                });

                if (response.success && response.data[postId]) {
                    const scores = response.data[postId];
                    this.updateScoreDisplay('title', scores.title_score || 0);
                    this.updateScoreDisplay('description', scores.description_score || 0);
                }
            } catch (error) {
                console.error('Failed to load scores:', error);
            }
        }

        /**
         * Utility methods
         */
        makeAjaxRequest(action, data = {}) {
            return new Promise((resolve, reject) => {
                $.ajax({
                    url: aiSeoUltra.ajaxUrl,
                    type: 'POST',
                    data: {
                        action: action,
                        nonce: aiSeoUltra.nonce,
                        ...data
                    },
                    success: resolve,
                    error: (xhr, status, error) => reject(new Error(error))
                });
            });
        }

        setButtonLoading($button, loading) {
            if (loading) {
                $button.prop('disabled', true)
                       .addClass('ai-seo-loading')
                       .append('<span class="ai-seo-spinner"></span>');
            } else {
                $button.prop('disabled', false)
                       .removeClass('ai-seo-loading')
                       .find('.ai-seo-spinner').remove();
            }
        }

        showNotice(message, type = 'info') {
            const $notice = $(`
                <div class="ai-seo-status ${type}">
                    ${message}
                </div>
            `);

            $('.ai-seo-notices').append($notice);
            
            setTimeout(() => {
                $notice.fadeOut(() => $notice.remove());
            }, 5000);
        }

        getCurrentPostId() {
            return $('#post_ID').val() || aiSeoUltra.postId || null;
        }

        getSelectedPosts() {
            return $('.check-column input:checked').map(function() {
                return $(this).val();
            }).get();
        }

        serializeFormData($form) {
            const formData = {};
            $form.serializeArray().forEach(item => {
                formData[item.name] = item.value;
            });
            return formData;
        }

        createProgressContainer() {
            const $container = $(`
                <div class="ai-seo-progress-container">
                    <div class="ai-seo-progress-bar">
                        <div class="ai-seo-progress-fill" style="width: 0%"></div>
                    </div>
                    <div class="ai-seo-progress-text">0%</div>
                </div>
            `);
            
            $('body').append($container);
            return $container;
        }

        updateProgress($container, current, total) {
            const percentage = Math.round((current / total) * 100);
            $container.find('.ai-seo-progress-fill').css('width', percentage + '%');
            $container.find('.ai-seo-progress-text').text(`${current}/${total} (${percentage}%)`);
        }

        initTooltips() {
            $('[data-tooltip]').each(function() {
                $(this).attr('title', $(this).data('tooltip'));
            });
        }

        initProgressBars() {
            $('.ai-seo-score-fill').each(function() {
                const $fill = $(this);
                const width = $fill.data('width') || 0;
                setTimeout(() => {
                    $fill.css('width', width + '%');
                }, 100);
            });
        }

        initCharacterCounters() {
            $('.ai-seo-field textarea, .ai-seo-field input[type="text"]').each(function() {
                const $field = $(this);
                const maxLength = $field.attr('maxlength');
                
                if (maxLength) {
                    const $counter = $(`<div class="ai-seo-char-counter">0/${maxLength}</div>`);
                    $field.after($counter);
                    
                    $field.on('input', function() {
                        const length = $(this).val().length;
                        $counter.text(`${length}/${maxLength}`);
                        
                        if (length > maxLength * 0.9) {
                            $counter.addClass('warning');
                        } else {
                            $counter.removeClass('warning');
                        }
                    }).trigger('input');
                }
            });
        }

        debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        delay(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }
    }

    /**
     * Initialize when document is ready
     */
    $(document).ready(() => {
        window.aiSeoUltraAdmin = new AiSeoUltraAdmin();
    });

})(jQuery);
