import React from 'react';
import { createRoot } from 'react-dom/client';
import { QueryClient, QueryClientProvider } from 'react-query';
import { Toaster } from 'react-hot-toast';
import { ErrorBoundary } from '@components/ErrorBoundary';
import { ThemeProvider } from '@components/ThemeProvider';
import { WebSocketProvider } from '@components/WebSocketProvider';
import { CommandCenter } from '@components/CommandCenter';
import { AccessibilityProvider } from '@components/AccessibilityProvider';
import '@styles/global.css';
import '@styles/themes.css';
import '@styles/accessibility.css';

// Create React Query client with optimized settings
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
      retry: (failureCount, error) => {
        if (error.status === 404 || error.status === 403) return false;
        return failureCount < 3;
      },
      refetchOnWindowFocus: false,
      refetchOnMount: false,
      refetchOnReconnect: 'always'
    },
    mutations: {
      retry: 1
    }
  }
});

// Performance monitoring
if (process.env.NODE_ENV === 'production') {
  // Web Vitals monitoring
  import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
    getCLS(console.log);
    getFID(console.log);
    getFCP(console.log);
    getLCP(console.log);
    getTTFB(console.log);
  });
}

// Error reporting
window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason);
  // Send to error tracking service in production
});

// WordPress integration globals
window.UFIOCommandCenter = {
  version: '1.0.0',
  apiUrl: window.wpApiSettings?.root || '/wp-json/wp/v2/',
  nonce: window.wpApiSettings?.nonce || '',
  pluginUrl: window.ufioSettings?.pluginUrl || '',
  wsUrl: window.ufioSettings?.wsUrl || 'ws://localhost:8080',
  user: window.ufioSettings?.currentUser || {},
  capabilities: window.ufioSettings?.capabilities || {},
  settings: window.ufioSettings?.settings || {}
};

// App component with all providers
function App() {
  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <ThemeProvider>
          <AccessibilityProvider>
            <WebSocketProvider url={window.UFIOCommandCenter.wsUrl}>
              <CommandCenter />
              <Toaster
                position="top-right"
                toastOptions={{
                  duration: 4000,
                  style: {
                    background: 'var(--ufio-surface)',
                    color: 'var(--ufio-on-surface)',
                    border: '1px solid var(--ufio-outline)',
                    borderRadius: '12px',
                    fontSize: '14px',
                    fontWeight: '500'
                  },
                  success: {
                    iconTheme: {
                      primary: 'var(--ufio-success)',
                      secondary: 'var(--ufio-on-success)'
                    }
                  },
                  error: {
                    iconTheme: {
                      primary: 'var(--ufio-error)',
                      secondary: 'var(--ufio-on-error)'
                    }
                  }
                }}
              />
            </WebSocketProvider>
          </AccessibilityProvider>
        </ThemeProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
}

// Initialize app
const container = document.getElementById('ufio-command-center');
if (container) {
  const root = createRoot(container);
  
  // Render with React 18 concurrent features
  root.render(<App />);
  
  // Service worker registration for caching
  if ('serviceWorker' in navigator && process.env.NODE_ENV === 'production') {
    window.addEventListener('load', () => {
      navigator.serviceWorker.register('/sw.js')
        .then((registration) => {
          console.log('SW registered: ', registration);
        })
        .catch((registrationError) => {
          console.log('SW registration failed: ', registrationError);
        });
    });
  }
} else {
  console.error('UFIO Command Center container not found');
}

// Hot module replacement for development
if (module.hot) {
  module.hot.accept();
}
