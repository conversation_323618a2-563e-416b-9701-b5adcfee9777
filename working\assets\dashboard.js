/**
 * UFIO Pro Dashboard - Enhanced JavaScript
 * Real-time updates, animations, and interactive features
 */

(function($) {
    'use strict';

    // Main Dashboard object
    window.UFIODashboard = {
        
        // Configuration
        config: {
            refreshInterval: 30000, // 30 seconds
            animationDuration: 300,
            chartColors: {
                primary: '#667eea',
                success: '#48bb78',
                warning: '#ed8936',
                error: '#f56565',
                info: '#4299e1'
            }
        },
        
        // State
        state: {
            isRefreshing: false,
            activityPaused: false,
            charts: {},
            refreshTimer: null,
            activityTimer: null
        },
        
        // Initialize dashboard
        init: function() {
            this.bindEvents();
            this.initCharts();
            this.startRealTimeUpdates();
            this.initActivityFeed();
            this.showWelcomeMessage();

            // Initialize Post URLs page if present
            if ($('#posts-table').length) {
                this.initPostUrlsPage();
            }
        },
        
        // Bind event handlers
        bindEvents: function() {
            // Refresh buttons
            $(document).on('click', '#ufio-refresh-all', this.refreshAll.bind(this));
            $(document).on('click', '.ufio-btn-icon', this.refreshCard.bind(this));
            
            // Quick actions
            $(document).on('click', '#ufio-start-batch', this.startBatchProcessing.bind(this));
            $(document).on('click', '#ufio-optimize-cache', this.optimizeCache.bind(this));
            $(document).on('click', '#ufio-recalc-priorities', this.recalculatePriorities.bind(this));
            $(document).on('click', '#ufio-test-microservice', this.testMicroservice.bind(this));
            
            // Activity feed controls
            $(document).on('click', '#ufio-pause-activity', this.toggleActivityFeed.bind(this));
            $(document).on('click', '#ufio-clear-activity', this.clearActivityFeed.bind(this));
            
            // Priority post processing
            $(document).on('click', '[data-post-id]', this.processSinglePost.bind(this));

            // Post URLs page functionality
            $(document).on('click', '#ufio-apply-filters', this.loadPostUrls.bind(this));
            $(document).on('click', '#ufio-clear-filters', this.clearFilters.bind(this));
            $(document).on('click', '#ufio-refresh-post-list', this.loadPostUrls.bind(this));
            $(document).on('click', '.ufio-insert-images-btn', this.showImageInsertionModal.bind(this));
            $(document).on('click', '#ufio-confirm-insertion', this.confirmImageInsertion.bind(this));
            $(document).on('click', '#ufio-cancel-insertion, .ufio-modal-close', this.hideImageInsertionModal.bind(this));
            
            // Window events
            $(window).on('focus', this.onWindowFocus.bind(this));
            $(window).on('blur', this.onWindowBlur.bind(this));
        },
        
        // Initialize charts
        initCharts: function() {
            this.initProcessingChart();
            this.initPerformanceChart();
        },
        
        // Initialize processing overview chart
        initProcessingChart: function() {
            const ctx = document.getElementById('processing-chart');
            if (!ctx) return;
            
            this.state.charts.processing = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'Posts Processed',
                        data: [],
                        borderColor: this.config.chartColors.primary,
                        backgroundColor: this.config.chartColors.primary + '20',
                        tension: 0.4,
                        fill: true
                    }, {
                        label: 'Success Rate',
                        data: [],
                        borderColor: this.config.chartColors.success,
                        backgroundColor: this.config.chartColors.success + '20',
                        tension: 0.4,
                        fill: true,
                        yAxisID: 'y1'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        }
                    },
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            grid: {
                                drawOnChartArea: false,
                            },
                        }
                    },
                    animation: {
                        duration: this.config.animationDuration
                    }
                }
            });
            
            // Load initial data
            this.updateProcessingChart();
        },
        
        // Start real-time updates
        startRealTimeUpdates: function() {
            this.refreshDashboardStats();
            
            this.state.refreshTimer = setInterval(() => {
                if (!this.state.isRefreshing) {
                    this.refreshDashboardStats();
                }
            }, this.config.refreshInterval);
        },
        
        // Refresh all dashboard data
        refreshAll: function() {
            if (this.state.isRefreshing) return;
            
            this.state.isRefreshing = true;
            this.showLoadingState();
            
            Promise.all([
                this.refreshDashboardStats(),
                this.refreshQueueStats(),
                this.refreshPerformanceMetrics(),
                this.refreshTopPriorityPosts()
            ]).then(() => {
                this.hideLoadingState();
                this.showNotification('success', ufioAjax.strings.updated);
            }).catch((error) => {
                this.hideLoadingState();
                this.showNotification('error', ufioAjax.strings.error);
                console.error('Dashboard refresh error:', error);
            }).finally(() => {
                this.state.isRefreshing = false;
            });
        },
        
        // Refresh dashboard statistics
        refreshDashboardStats: function() {
            return $.ajax({
                url: ufioAjax.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'ufio_dashboard_stats',
                    nonce: ufioAjax.nonce
                }
            }).done((response) => {
                if (response.success) {
                    this.updateMetricCards(response.data);
                    this.updateSystemStatus(response.data);
                }
            });
        },
        
        // Update metric cards with animation
        updateMetricCards: function(data) {
            const metrics = {
                'total-posts': data.total_posts,
                'coverage-rate': data.coverage_percentage + '%',
                'queue-size': data.queue_pending,
                'efficiency-score': data.efficiency_score + '%'
            };
            
            Object.entries(metrics).forEach(([id, value]) => {
                const $element = $('#' + id);
                if ($element.length) {
                    this.animateNumber($element, value);
                }
            });
        },
        
        // Animate number changes
        animateNumber: function($element, newValue) {
            const currentValue = $element.text().replace(/[^\d.-]/g, '');
            const targetValue = String(newValue).replace(/[^\d.-]/g, '');
            
            if (currentValue === targetValue) return;
            
            $element.addClass('ufio-updating');
            
            $({ value: parseFloat(currentValue) || 0 }).animate({
                value: parseFloat(targetValue) || 0
            }, {
                duration: this.config.animationDuration,
                step: function(val) {
                    if (String(newValue).includes('%')) {
                        $element.text(Math.round(val) + '%');
                    } else {
                        $element.text(Math.round(val).toLocaleString());
                    }
                },
                complete: function() {
                    $element.removeClass('ufio-updating');
                    $element.text(newValue);
                }
            });
        },
        
        // Update processing chart
        updateProcessingChart: function() {
            $.ajax({
                url: ufioAjax.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'ufio_performance_metrics',
                    nonce: ufioAjax.nonce
                }
            }).done((response) => {
                if (response.success && this.state.charts.processing) {
                    const chart = this.state.charts.processing;
                    const data = response.data;
                    
                    // Update chart data (this would be real data from the API)
                    chart.data.labels = this.generateTimeLabels(24); // Last 24 hours
                    chart.data.datasets[0].data = this.generateSampleData(24);
                    chart.data.datasets[1].data = this.generateSampleData(24, 80, 100);
                    
                    chart.update('none');
                }
            });
        },
        
        // Generate time labels
        generateTimeLabels: function(hours) {
            const labels = [];
            for (let i = hours; i >= 0; i--) {
                const date = new Date();
                date.setHours(date.getHours() - i);
                labels.push(date.toLocaleTimeString('en-US', { 
                    hour: '2-digit', 
                    minute: '2-digit' 
                }));
            }
            return labels;
        },
        
        // Generate sample data (replace with real data)
        generateSampleData: function(count, min = 0, max = 100) {
            const data = [];
            for (let i = 0; i < count; i++) {
                data.push(Math.floor(Math.random() * (max - min + 1)) + min);
            }
            return data;
        },
        
        // Initialize activity feed
        initActivityFeed: function() {
            this.startActivityUpdates();
        },
        
        // Start activity feed updates
        startActivityUpdates: function() {
            this.state.activityTimer = setInterval(() => {
                if (!this.state.activityPaused) {
                    this.addActivityItem();
                }
            }, 5000); // Add new activity every 5 seconds
        },
        
        // Add activity item
        addActivityItem: function() {
            const activities = [
                { type: 'success', icon: '✅', title: 'Post processed successfully', meta: 'Post ID: 1234' },
                { type: 'info', icon: '🔄', title: 'Batch processing started', meta: '50 posts in queue' },
                { type: 'warning', icon: '⚠️', title: 'API rate limit approaching', meta: '80% of quota used' },
                { type: 'success', icon: '🎯', title: 'Featured image assigned', meta: 'Post: "How to optimize images"' }
            ];
            
            const activity = activities[Math.floor(Math.random() * activities.length)];
            const timestamp = new Date().toLocaleTimeString();
            
            const $item = $(`
                <div class="ufio-activity-item ufio-slide-in">
                    <div class="ufio-activity-icon ${activity.type}">
                        ${activity.icon}
                    </div>
                    <div class="ufio-activity-content">
                        <div class="ufio-activity-title">${activity.title}</div>
                        <div class="ufio-activity-meta">${activity.meta}</div>
                    </div>
                    <div class="ufio-activity-time">${timestamp}</div>
                </div>
            `);
            
            const $feed = $('#ufio-activity-feed');
            $feed.prepend($item);
            
            // Limit to 20 items
            $feed.children().slice(20).remove();
        },
        
        // Toggle activity feed
        toggleActivityFeed: function() {
            this.state.activityPaused = !this.state.activityPaused;
            const $button = $('#ufio-pause-activity');
            
            if (this.state.activityPaused) {
                $button.html('<span class="dashicons dashicons-controls-play"></span> Resume');
            } else {
                $button.html('<span class="dashicons dashicons-controls-pause"></span> Pause');
            }
        },
        
        // Clear activity feed
        clearActivityFeed: function() {
            $('#ufio-activity-feed').empty();
            this.showNotification('info', 'Activity feed cleared');
        },
        
        // Start batch processing
        startBatchProcessing: function() {
            this.showNotification('info', 'Starting batch processing...');
            
            $.ajax({
                url: ufioAjax.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'ufio_bulk_process',
                    nonce: ufioAjax.nonce,
                    batch_size: 20
                }
            }).done((response) => {
                if (response.success) {
                    this.showNotification('success', 'Batch processing completed');
                    this.refreshDashboardStats();
                } else {
                    this.showNotification('error', response.data.message);
                }
            });
        },
        
        // Optimize cache
        optimizeCache: function() {
            this.showNotification('info', 'Optimizing cache...');
            
            $.ajax({
                url: ufioAjax.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'ufio_clear_cache',
                    nonce: ufioAjax.nonce
                }
            }).done((response) => {
                if (response.success) {
                    this.showNotification('success', 'Cache optimized successfully');
                } else {
                    this.showNotification('error', response.data.message);
                }
            });
        },
        
        // Recalculate priorities
        recalculatePriorities: function() {
            this.showNotification('info', 'Recalculating priorities...');
            
            // This would trigger priority recalculation
            setTimeout(() => {
                this.showNotification('success', 'Priorities recalculated');
                this.refreshTopPriorityPosts();
            }, 2000);
        },
        
        // Test microservice
        testMicroservice: function() {
            this.showNotification('info', 'Testing microservice connection...');
            
            $.ajax({
                url: ufioAjax.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'ufio_microservice_status',
                    nonce: ufioAjax.nonce
                }
            }).done((response) => {
                if (response.success) {
                    if (response.data.status === 'healthy') {
                        this.showNotification('success', 'Microservice is healthy');
                    } else {
                        this.showNotification('warning', 'Microservice: ' + response.data.message);
                    }
                } else {
                    this.showNotification('error', 'Microservice test failed');
                }
            });
        },
        
        // Process single post
        processSinglePost: function(e) {
            const postId = $(e.currentTarget).data('post-id');
            const $button = $(e.currentTarget);
            
            $button.prop('disabled', true).text('Processing...');
            
            $.ajax({
                url: ufioAjax.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'ufio_process_single',
                    nonce: ufioAjax.nonce,
                    post_id: postId
                }
            }).done((response) => {
                if (response.success) {
                    this.showNotification('success', 'Post processed successfully');
                    $button.text('Completed').addClass('success');
                    this.refreshDashboardStats();
                } else {
                    this.showNotification('error', response.data.message);
                    $button.text('Failed').addClass('error');
                }
            }).always(() => {
                setTimeout(() => {
                    $button.prop('disabled', false).text('Process').removeClass('success error');
                }, 3000);
            });
        },
        
        // Show loading state
        showLoadingState: function() {
            $('.ufio-metric-card, .ufio-dashboard-card').addClass('ufio-loading');
        },
        
        // Hide loading state
        hideLoadingState: function() {
            $('.ufio-metric-card, .ufio-dashboard-card').removeClass('ufio-loading');
        },
        
        // Show notification
        showNotification: function(type, message, duration = 4000) {
            const $notification = $(`
                <div class="ufio-notification ufio-notification-${type}">
                    <div class="ufio-notification-content">
                        <span class="ufio-notification-icon">${this.getNotificationIcon(type)}</span>
                        <span class="ufio-notification-message">${message}</span>
                    </div>
                    <button class="ufio-notification-close">&times;</button>
                </div>
            `);
            
            // Add to container
            if (!$('#ufio-notifications').length) {
                $('body').append('<div id="ufio-notifications"></div>');
            }
            
            $('#ufio-notifications').append($notification);
            
            // Animate in
            setTimeout(() => $notification.addClass('show'), 10);
            
            // Auto remove
            setTimeout(() => {
                $notification.removeClass('show');
                setTimeout(() => $notification.remove(), 300);
            }, duration);
            
            // Manual close
            $notification.find('.ufio-notification-close').on('click', () => {
                $notification.removeClass('show');
                setTimeout(() => $notification.remove(), 300);
            });
        },
        
        // Get notification icon
        getNotificationIcon: function(type) {
            const icons = {
                success: '✅',
                error: '❌',
                warning: '⚠️',
                info: 'ℹ️'
            };
            return icons[type] || 'ℹ️';
        },
        
        // Show welcome message
        showWelcomeMessage: function() {
            if (!sessionStorage.getItem('ufio_dashboard_welcome')) {
                setTimeout(() => {
                    this.showNotification('success', 'Welcome to UFIO Pro Dashboard! 🚀', 6000);
                    sessionStorage.setItem('ufio_dashboard_welcome', 'true');
                }, 1000);
            }
        },
        
        // Window focus handler
        onWindowFocus: function() {
            if (!this.state.refreshTimer) {
                this.startRealTimeUpdates();
            }
        },
        
        // Window blur handler
        onWindowBlur: function() {
            // Optionally pause updates when window is not focused
        },
        
        // Refresh specific card
        refreshCard: function(e) {
            const $card = $(e.currentTarget).closest('.ufio-dashboard-card');
            $card.addClass('ufio-loading');
            
            setTimeout(() => {
                $card.removeClass('ufio-loading');
                this.showNotification('info', 'Card refreshed');
            }, 1000);
        },
        
        // Refresh queue stats
        refreshQueueStats: function() {
            return $.ajax({
                url: ufioAjax.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'ufio_queue_stats',
                    nonce: ufioAjax.nonce
                }
            });
        },
        
        // Refresh performance metrics
        refreshPerformanceMetrics: function() {
            return $.ajax({
                url: ufioAjax.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'ufio_performance_metrics',
                    nonce: ufioAjax.nonce
                }
            });
        },
        
        // Refresh top priority posts
        refreshTopPriorityPosts: function() {
            return $.ajax({
                url: ufioAjax.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'ufio_top_priority_posts',
                    nonce: ufioAjax.nonce
                }
            }).done((response) => {
                if (response.success) {
                    this.updatePriorityPostsList(response.data);
                }
            });
        },
        
        // Update priority posts list
        updatePriorityPostsList: function(posts) {
            const $list = $('#priority-posts-list');
            $list.empty();
            
            posts.slice(0, 5).forEach(post => {
                const $item = $(`
                    <div class="ufio-priority-item ufio-slide-in">
                        <div class="ufio-priority-score">${parseFloat(post.priority_score).toFixed(1)}</div>
                        <div class="ufio-priority-content">
                            <div class="ufio-priority-title">${post.post_title}</div>
                            <div class="ufio-priority-meta">
                                Traffic: ${parseFloat(post.traffic_weight).toFixed(1)} | 
                                SEO: ${parseFloat(post.seo_potential).toFixed(1)}
                            </div>
                        </div>
                        <button class="ufio-btn-sm primary" data-post-id="${post.post_id}">
                            Process
                        </button>
                    </div>
                `);
                $list.append($item);
            });
        },
        
        // Update system status
        updateSystemStatus: function(data) {
            const $status = $('#ufio-system-status');
            const $dot = $status.find('.ufio-status-dot');
            const $text = $status.find('span:last-child');
            
            // Determine status based on data
            if (data.efficiency_score >= 80) {
                $dot.removeClass().addClass('ufio-status-dot success');
                $text.text('System Healthy');
            } else if (data.efficiency_score >= 60) {
                $dot.removeClass().addClass('ufio-status-dot warning');
                $text.text('System Warning');
            } else {
                $dot.removeClass().addClass('ufio-status-dot error');
                $text.text('System Issues');
            }
        },

        // POST URLS PAGE FUNCTIONALITY

        /**
         * Initialize Post URLs page
         */
        initPostUrlsPage: function() {
            this.loadPostUrls();
        },

        /**
         * Load post URLs with filters
         */
        loadPostUrls: function() {
            const filters = {
                post_type: $('#post-type-filter').val(),
                image_count_filter: $('#image-count-filter').val(),
                featured_filter: $('#featured-image-filter').val(),
                search: $('#search-posts').val(),
                page: 1,
                per_page: 20
            };

            $('#posts-table-body').html('<tr><td colspan="9" class="ufio-loading-row"><div class="ufio-loading-spinner"></div>Loading posts...</td></tr>');

            $.ajax({
                url: ufioAjax.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'ufio_get_post_urls',
                    nonce: ufioAjax.nonce,
                    ...filters
                }
            }).done((response) => {
                if (response.success) {
                    this.renderPostsTable(response.data.posts);
                    this.updatePostsCount(response.data.total);
                } else {
                    this.showNotification('error', response.data.message);
                }
            }).fail(() => {
                this.showNotification('error', 'Failed to load posts');
            });
        },

        /**
         * Render posts table
         */
        renderPostsTable: function(posts) {
            const $tbody = $('#posts-table-body');
            $tbody.empty();

            if (posts.length === 0) {
                $tbody.html('<tr><td colspan="9" class="ufio-loading-row">No posts found matching your criteria.</td></tr>');
                return;
            }

            posts.forEach(post => {
                const imageCountClass = post.image_count === 0 ? 'no-images' : 'has-images';
                const featuredClass = post.has_featured ? 'has-featured' : 'no-featured';
                const featuredText = post.has_featured ? 'Yes' : 'No';

                const row = `
                    <tr data-post-id="${post.id}">
                        <td><input type="checkbox" class="post-checkbox" value="${post.id}"></td>
                        <td>
                            <strong>${post.title}</strong>
                            <div class="ufio-post-meta">ID: ${post.id}</div>
                        </td>
                        <td><a href="${post.url}" target="_blank" class="ufio-post-url">${post.url}</a></td>
                        <td>${post.type}</td>
                        <td><span class="ufio-image-count ${imageCountClass}">${post.image_count} images</span></td>
                        <td><span class="ufio-featured-status ${featuredClass}">${featuredText}</span></td>
                        <td>${post.word_count} words</td>
                        <td>${new Date(post.modified).toLocaleDateString()}</td>
                        <td>
                            <button class="ufio-btn-sm primary ufio-insert-images-btn"
                                    data-post-id="${post.id}"
                                    ${!post.can_insert_images ? 'disabled' : ''}>
                                Insert Images
                            </button>
                            <a href="${post.edit_url}" class="ufio-btn-sm secondary" target="_blank">Edit</a>
                        </td>
                    </tr>
                `;
                $tbody.append(row);
            });
        },

        /**
         * Update posts count
         */
        updatePostsCount: function(total) {
            $('#posts-count').text(`${total} posts found`);
        },

        /**
         * Clear filters
         */
        clearFilters: function() {
            $('#post-type-filter').val('');
            $('#image-count-filter').val('');
            $('#featured-image-filter').val('');
            $('#search-posts').val('');
            this.loadPostUrls();
        },

        /**
         * Show image insertion modal
         */
        showImageInsertionModal: function(e) {
            const postId = $(e.currentTarget).data('post-id');
            const postTitle = $(e.currentTarget).closest('tr').find('strong').text();

            // Store current post ID
            this.currentPostId = postId;

            // Update modal content
            $('#ufio-insertion-preview').html(`
                <div class="ufio-post-preview">
                    <h4>Post: ${postTitle}</h4>
                    <p>This will automatically insert relevant images from your media library into the post content at strategic locations based on headings and content structure.</p>
                    <div class="ufio-preview-info">
                        <strong>How it works:</strong>
                        <ul>
                            <li>Analyzes post content and headings</li>
                            <li>Finds relevant images from media library</li>
                            <li>Inserts images at optimal positions</li>
                            <li>Generates SEO-optimized alt text</li>
                        </ul>
                    </div>
                </div>
            `);

            // Show modal
            $('#ufio-image-insertion-modal').show();
        },

        /**
         * Hide image insertion modal
         */
        hideImageInsertionModal: function() {
            $('#ufio-image-insertion-modal').hide();
            this.currentPostId = null;
        },

        /**
         * Confirm image insertion
         */
        confirmImageInsertion: function() {
            if (!this.currentPostId) return;

            const options = {
                post_id: this.currentPostId,
                insert_after_intro: $('#insert-after-intro').is(':checked'),
                insert_before_headings: $('#insert-before-headings').is(':checked'),
                insert_in_long_sections: $('#insert-in-long-sections').is(':checked'),
                optimize_alt_text: $('#optimize-alt-text').is(':checked')
            };

            // Show loading state
            $('#ufio-confirm-insertion').prop('disabled', true).text('Inserting...');

            $.ajax({
                url: ufioAjax.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'ufio_insert_content_image',
                    nonce: ufioAjax.nonce,
                    ...options
                }
            }).done((response) => {
                if (response.success) {
                    this.showNotification('success', response.data.message);
                    this.hideImageInsertionModal();
                    this.loadPostUrls(); // Refresh the table
                } else {
                    this.showNotification('error', response.data.message);
                }
            }).fail(() => {
                this.showNotification('error', 'Failed to insert images');
            }).always(() => {
                $('#ufio-confirm-insertion').prop('disabled', false).text('Insert Images');
            });
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        if ($('.ufio-dashboard-wrap').length) {
            UFIODashboard.init();
        }
    });

})(jQuery);
