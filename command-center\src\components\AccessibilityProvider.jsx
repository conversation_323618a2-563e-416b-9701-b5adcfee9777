import React, { createContext, useContext, useEffect, useState } from 'react';

const AccessibilityContext = createContext();

export function useAccessibility() {
  const context = useContext(AccessibilityContext);
  if (!context) {
    throw new Error('useAccessibility must be used within AccessibilityProvider');
  }
  return context;
}

export function AccessibilityProvider({ children }) {
  const [reducedMotion, setReducedMotion] = useState(false);
  const [highContrast, setHighContrast] = useState(false);
  const [fontSize, setFontSize] = useState('normal');
  const [focusVisible, setFocusVisible] = useState(false);
  const [screenReader, setScreenReader] = useState(false);
  
  // Detect system accessibility preferences
  useEffect(() => {
    // Reduced motion preference
    const motionQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    const handleMotionChange = (e) => setReducedMotion(e.matches);
    setReducedMotion(motionQuery.matches);
    motionQuery.addEventListener('change', handleMotionChange);
    
    // High contrast preference
    const contrastQuery = window.matchMedia('(prefers-contrast: high)');
    const handleContrastChange = (e) => setHighContrast(e.matches);
    setHighContrast(contrastQuery.matches);
    contrastQuery.addEventListener('change', handleContrastChange);
    
    // Screen reader detection
    const detectScreenReader = () => {
      // Check for common screen reader indicators
      const hasScreenReader = 
        navigator.userAgent.includes('NVDA') ||
        navigator.userAgent.includes('JAWS') ||
        navigator.userAgent.includes('VoiceOver') ||
        window.speechSynthesis ||
        document.querySelector('[aria-live]') !== null;
      
      setScreenReader(hasScreenReader);
    };
    
    detectScreenReader();
    
    return () => {
      motionQuery.removeEventListener('change', handleMotionChange);
      contrastQuery.removeEventListener('change', handleContrastChange);
    };
  }, []);
  
  // Load saved accessibility preferences
  useEffect(() => {
    const savedPrefs = localStorage.getItem('ufio-accessibility');
    if (savedPrefs) {
      try {
        const prefs = JSON.parse(savedPrefs);
        if (prefs.fontSize) setFontSize(prefs.fontSize);
        if (prefs.highContrast !== undefined) setHighContrast(prefs.highContrast);
        if (prefs.reducedMotion !== undefined) setReducedMotion(prefs.reducedMotion);
      } catch (error) {
        console.error('Failed to load accessibility preferences:', error);
      }
    }
  }, []);
  
  // Apply accessibility settings to document
  useEffect(() => {
    const root = document.documentElement;
    
    // Reduced motion
    root.style.setProperty('--motion-duration', reducedMotion ? '0ms' : '300ms');
    root.setAttribute('data-reduced-motion', reducedMotion);
    
    // High contrast
    root.setAttribute('data-high-contrast', highContrast);
    
    // Font size
    const fontSizeMap = {
      small: '14px',
      normal: '16px',
      large: '18px',
      xlarge: '20px'
    };
    root.style.setProperty('--base-font-size', fontSizeMap[fontSize]);
    
    // Screen reader optimizations
    if (screenReader) {
      root.setAttribute('data-screen-reader', 'true');
    }
    
    // Save preferences
    const prefs = {
      fontSize,
      highContrast,
      reducedMotion
    };
    localStorage.setItem('ufio-accessibility', JSON.stringify(prefs));
  }, [reducedMotion, highContrast, fontSize, screenReader]);
  
  // Focus management
  useEffect(() => {
    let isUsingKeyboard = false;
    
    const handleKeyDown = (e) => {
      if (e.key === 'Tab') {
        isUsingKeyboard = true;
        setFocusVisible(true);
      }
    };
    
    const handleMouseDown = () => {
      isUsingKeyboard = false;
      setFocusVisible(false);
    };
    
    const handleFocus = (e) => {
      if (isUsingKeyboard) {
        e.target.setAttribute('data-focus-visible', 'true');
      }
    };
    
    const handleBlur = (e) => {
      e.target.removeAttribute('data-focus-visible');
    };
    
    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('mousedown', handleMouseDown);
    document.addEventListener('focus', handleFocus, true);
    document.addEventListener('blur', handleBlur, true);
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('mousedown', handleMouseDown);
      document.removeEventListener('focus', handleFocus, true);
      document.removeEventListener('blur', handleBlur, true);
    };
  }, []);
  
  // Announce to screen readers
  const announce = (message, priority = 'polite') => {
    const announcer = document.createElement('div');
    announcer.setAttribute('aria-live', priority);
    announcer.setAttribute('aria-atomic', 'true');
    announcer.className = 'sr-only';
    announcer.textContent = message;
    
    document.body.appendChild(announcer);
    
    setTimeout(() => {
      document.body.removeChild(announcer);
    }, 1000);
  };
  
  // Skip to content functionality
  const skipToContent = (targetId) => {
    const target = document.getElementById(targetId);
    if (target) {
      target.focus();
      target.scrollIntoView({ behavior: reducedMotion ? 'auto' : 'smooth' });
      announce(`Skipped to ${target.getAttribute('aria-label') || targetId}`);
    }
  };
  
  // Keyboard navigation helpers
  const handleArrowNavigation = (e, items, currentIndex, onSelect) => {
    let newIndex = currentIndex;
    
    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        newIndex = Math.min(currentIndex + 1, items.length - 1);
        break;
      case 'ArrowUp':
        e.preventDefault();
        newIndex = Math.max(currentIndex - 1, 0);
        break;
      case 'Home':
        e.preventDefault();
        newIndex = 0;
        break;
      case 'End':
        e.preventDefault();
        newIndex = items.length - 1;
        break;
      case 'Enter':
      case ' ':
        e.preventDefault();
        onSelect(items[currentIndex]);
        return;
      default:
        return;
    }
    
    if (newIndex !== currentIndex) {
      onSelect(items[newIndex]);
    }
  };
  
  // Color contrast checker
  const checkContrast = (foreground, background) => {
    // Simplified contrast ratio calculation
    const getLuminance = (color) => {
      const rgb = parseInt(color.slice(1), 16);
      const r = (rgb >> 16) & 0xff;
      const g = (rgb >> 8) & 0xff;
      const b = (rgb >> 0) & 0xff;
      
      const [rs, gs, bs] = [r, g, b].map(c => {
        c = c / 255;
        return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
      });
      
      return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
    };
    
    const l1 = getLuminance(foreground);
    const l2 = getLuminance(background);
    const ratio = (Math.max(l1, l2) + 0.05) / (Math.min(l1, l2) + 0.05);
    
    return {
      ratio,
      aa: ratio >= 4.5,
      aaa: ratio >= 7
    };
  };
  
  const value = {
    reducedMotion,
    highContrast,
    fontSize,
    focusVisible,
    screenReader,
    setReducedMotion,
    setHighContrast,
    setFontSize,
    announce,
    skipToContent,
    handleArrowNavigation,
    checkContrast
  };
  
  return (
    <AccessibilityContext.Provider value={value}>
      {children}
    </AccessibilityContext.Provider>
  );
}
